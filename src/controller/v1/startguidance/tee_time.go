package v1startguidance

import (
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/logging"
	"mi-restful-api/model/player"
	"mi-restful-api/model/startguidance"
	"mi-restful-api/model/tee"
	"mi-restful-api/repository"
	req "mi-restful-api/request/startguidance"
	resp "mi-restful-api/response/startguidance"
	"mi-restful-api/utils"
	"mi-restful-api/utils/common"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	courseRepo "mi-restful-api/repository/course"
	playerRepo "mi-restful-api/repository/player"
)

type TeeTime struct {
}

// Index
// @Summary Index
// @Description Index
// @Produce  json
// @Param data body req.TeeTimeReq true "Request payload"
// @Success 200 {object} resp.TeeTimeResp "response"
// @Failure 400 {object} resp.TeeTimeResp "Bad Request ,param error"
// @Failure 401 {object} resp.TeeTimeResp "Unauthorized"
// @Failure 404 {object} resp.TeeTimeResp "Not Found"
// @Failure 500 {object} resp.TeeTimeResp "Internal Server Error"
func (rec *TeeTime) Index(c *gin.Context) {
	var req req.TeeTimeReq
	var resp resp.TeeTimeResp
	requestId := common.GetRequestId(c)

	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		slog.Error("Failed to get web auth info", "error", err, "request", requestId)
		resp.Msg = err.Error()
		resp.Code = http.StatusUnauthorized
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		slog.Error("Failed to bind query", "error", err, "request", requestId)
		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	dateStr := time.Now().Format("20060102")
	officeKey := authInfo.OfficeKey

	// get player from dynamodb
	players, err := playerRepo.GetPlayersFromDynamo(dateStr, officeKey)
	if err != nil {
		slog.Error("PlayerListener.ListenPlayerMessage", "err", err)
		return
	}

	// get current tee sheet from dynamodb
	currentTeeSheets, err := playerRepo.TeeSheetsByDateStr(dateStr, officeKey)
	if err != nil {
		slog.Error("Failed to get current tee sheets", "error", err)
		return
	}

	// Get office information once before the loop
	db, err := client.GetMncDBClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "compe list course get db client error",
			"module":    "ListCourse",
			"office_id": authInfo.OfficeID,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handler := playerRepo.PlayerRepo{ConnMySQLMncdb: db}

	office, err := handler.GetOfficeByKey(officeKey)
	if err != nil {
		slog.Error("Failed to get office by office key", "error", err)
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// loop start
	teeSheets := []tee.TeeSheet{}

	slog.Debug("HandlePlayerMessage players count", "count", len(players))

	for _, player := range players {

		var cartNo int
		if player.Details.CartNo == "" {
			slog.Warn("HandlePlayerMessage no cart no", "player", player)
			continue
		} else {
			cartNo, err = strconv.Atoi(player.Details.CartNo)
			if err != nil {
				slog.Error("Failed to convert cart no to int", "error", err)
				continue
			}
		}

		teeId := player.Details.ScoreApplyConfig.Selected.TeeId
		if teeId == "" {
			teeId = player.Details.ScoreApplyConfig.Defaults.TeeId
		}

		playerNo, err := strconv.Atoi(player.Details.PlayerNo)
		if err != nil {
			slog.Error("Failed to convert player no to int", "error", err, "player", player)
			continue
		}

		gender, err := strconv.Atoi(player.Details.PlayerSex)
		if err != nil {
			slog.Error("Failed to convert gender to int", "error", err, "player", player)
			gender = 0
		}

		//Find player from currentTeeSheets by player no and cart no
		var currentTeePlayer *tee.TeePlayer
		for _, currentTeeSheet := range currentTeeSheets {
			currentCartNo, _ := strconv.Atoi(currentTeeSheet.Details.CartNo)
			if currentCartNo == cartNo {
				for _, tp := range currentTeeSheet.Details.TeePlayer {
					if tp.PlayerNo == playerNo {
						currentTeePlayer = &tp
						break
					}
				}
				if currentTeePlayer != nil {
					break
				}
			}
		}

		teePlayer := tee.TeePlayer{
			PlayerNo:    playerNo,
			PlayerName:  player.Details.PlayerName,
			Gender:      gender,
			Birthday:    player.Details.PlayerBirthDay,
			GlidNo:      player.Details.GlidNo,
			TeeId:       teeId,
			Hdcp:        player.Details.PrivateHDCP,
			HdcpIndex:   player.Details.WHSHDCP,
			OfficeKey:   officeKey,
			PlayDate:    dateStr,
			CartNo:      cartNo,
			LockerNo:    player.Details.LockerNo,
			ScoreHash:   player.Details.ScoreHash,
			OldCompeNo:  player.Details.CompeNo,
			PrivateHdcp: player.Details.PrivateHDCP,
			WHSHdcp:     player.Details.WHSHDCP,
		}

		// If current tee player exists, use its values when they're not empty
		if currentTeePlayer != nil {
			if currentTeePlayer.Hdcp != "" {
				teePlayer.Hdcp = currentTeePlayer.Hdcp
			}
			if currentTeePlayer.HdcpIndex != "" {
				teePlayer.HdcpIndex = currentTeePlayer.HdcpIndex
			}
			if currentTeePlayer.GlidNo != "nil" {
				teePlayer.GlidNo = currentTeePlayer.GlidNo
			}
			if currentTeePlayer.Birthday != "" {
				teePlayer.Birthday = currentTeePlayer.Birthday
			}
			if currentTeePlayer.Gender != 0 {
				teePlayer.Gender = currentTeePlayer.Gender
			}
		}

		scheduledStartTime := func() string {
			if st := player.Details.ScheduledStartTime; st != "" {
				return st
			}
			return "00:00"
		}()
		startSorkey := strings.ReplaceAll(scheduledStartTime, ":", "")

		// if sort key is the same then append teePlayer
		var existSortKey bool = false
		for i, teeSheet := range teeSheets {
			if teeSheet.SortKey == "tee_sheet_"+dateStr+"_"+player.Details.CartNo+"_"+startSorkey {
				teeSheets[i].Details.TeePlayer = append(teeSheets[i].Details.TeePlayer, teePlayer)
				existSortKey = true
				continue
			}
		}

		//TODO need to check the currect data for cart history
		histories, err := handler.GetPlayHistoryFromRDS(office.OfficeId, cartNo, dateStr)
		if err != nil {
			slog.Error("Failed to get play history from RDS", "error", err, "officeId", office.OfficeId, "cartNo", cartNo, "dateStr", dateStr)
			continue
		}

		slog.Debug("HandlePlayerMessage get play history", "histories", histories, "officeId", office.OfficeId, "cartNo", cartNo, "dateStr", dateStr)

		var startCourseIndex string = "" // 0,1,2..
		var startTime string = ""        // 00:00
		if len(histories) > 0 {
			//get first histories by created at
			firstHistory := histories[0]
			startCourseIndex = strconv.Itoa(firstHistory.CourseIndex)
			startTime = utils.TimeToStringByLayout(firstHistory.StartTime, "15:04")
		}

		if !existSortKey {
			teeSheetDetails := tee.TeeSheetDetail{
				CartNo:                    player.Details.CartNo,
				ScheduledStartCourseIndex: player.Details.ScheduledStartCourseIndex,
				ScheduledStartTime:        player.Details.ScheduledStartTime,
				StartCourseIndex:          startCourseIndex,
				StartTime:                 startTime,
				TeePlayer:                 []tee.TeePlayer{teePlayer},
			}

			teeSheets = append(teeSheets, tee.TeeSheet{
				PartitionKey:   officeKey,
				SortKey:        "tee_sheet_" + dateStr + "_" + player.Details.CartNo + "_" + startSorkey,
				Details:        teeSheetDetails,
				ExpirationTime: 0,
				UpdatedAt:      time.Now(),
			})
		}
	}

	slog.Debug("HandlePlayerMessage teeSheetDetails", "teeSheetDetails", teeSheets)

	// Convert teeSheets to TeeTimesData
	var teeTimes []startguidance.TeeTime
	for i, teeSheet := range teeSheets {
		// Convert TeePlayer to Player
		var players []startguidance.Player
		for _, teePlayer := range teeSheet.Details.TeePlayer {
			players = append(players, startguidance.Player{
				LockerNo: teePlayer.LockerNo,
			})
		}

		// Determine the time to display (prefer actual start time, fallback to scheduled)
		displayTime := teeSheet.Details.StartTime
		if displayTime == "" {
			displayTime = teeSheet.Details.ScheduledStartTime
		}
		if displayTime == "" {
			displayTime = "00:00"
		}

		// Determine status based on whether they have started
		status := "---" // Default: "Not arrived"
		if teeSheet.Details.StartTime != "" {
			status = "Tee" // "Arrived" if they have a start time
		}

		teeTimes = append(teeTimes, startguidance.TeeTime{
			No:      strconv.Itoa(i + 1), // Sequential number
			Time:    displayTime,
			Status:  status,
			CartNo:  teeSheet.Details.CartNo,
			Players: players,
		})
	}

	// Sort tee times by cart number for consistent ordering
	sort.Slice(teeTimes, func(i, j int) bool {
		cartNoI, _ := strconv.Atoi(teeTimes[i].CartNo)
		cartNoJ, _ := strconv.Atoi(teeTimes[j].CartNo)
		return cartNoI < cartNoJ
	})

	// Group teeTimes by course index
	courseGroups := make(map[string][]startguidance.TeeTime)

	for _, teeTime := range teeTimes {
		// Find the corresponding teeSheet to get course index
		var courseIndex string
		for _, teeSheet := range teeSheets {
			if teeSheet.Details.CartNo == teeTime.CartNo {
				// Prefer actual start course index, fallback to scheduled
				courseIndex = teeSheet.Details.StartCourseIndex
				if courseIndex == "" {
					courseIndex = teeSheet.Details.ScheduledStartCourseIndex
				}
				if courseIndex == "" {
					courseIndex = "0" // Default course index
				}
				break
			}
		}

		courseGroups[courseIndex] = append(courseGroups[courseIndex], teeTime)
	}

	// Get course names from CourseRepos
	courseHandler := courseRepo.CourseRepos{ConnMySQLMncdb: db}
	courses, err := courseHandler.ListCourse(authInfo.OfficeID)
	if err != nil {
		slog.Error("Failed to get courses", "error", err)
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// Create a map for quick course name lookup by course index
	courseMap := make(map[string]string)
	for _, course := range courses {
		courseMap[course.CourseIndex] = course.CourseName
	}

	// Put grouped teeTimes into TeeCourseData
	var teeCourseData []startguidance.TeeCourseData
	for courseIndex, groupedTeeTimes := range courseGroups {
		courseName := courseMap[courseIndex]
		if courseName == "" {
			courseName = "未定"
		}

		// Re-number the tee times within each course group
		for i := range groupedTeeTimes {
			groupedTeeTimes[i].No = strconv.Itoa(i + 1)
		}

		teeCourseData = append(teeCourseData, startguidance.TeeCourseData{
			CourseName:  courseName,
			CourseIndex: courseIndex,
			TeeTimes:    groupedTeeTimes,
		})
	}

	// Sort course data by course index for consistent ordering
	sort.Slice(teeCourseData, func(i, j int) bool {
		// Find course indices for comparison
		var indexI, indexJ string
		for courseIndex, courseName := range courseMap {
			if courseName == teeCourseData[i].CourseName {
				indexI = courseIndex
			}
			if courseName == teeCourseData[j].CourseName {
				indexJ = courseIndex
			}
		}
		courseIndexI, _ := strconv.Atoi(indexI)
		courseIndexJ, _ := strconv.Atoi(indexJ)
		return courseIndexI < courseIndexJ
	})

	resp.Code = 200
	resp.Msg = "success"
	resp.Data = &startguidance.TeeTimesData{
		CurrentTime: time.Now().Format("15:04"),
		TeeCourse:   teeCourseData,
		Information: "スタート案内情報", // "Start guidance information"
	}
	c.JSON(resp.Code, resp)

}

// Personal
// @Summary Personal
// @Description Personal
// @Produce  json
// @Param data body req.TeeTimePersonalReq true "Request payload"
// @Success 200 {object} resp.PlayerTeeResp "response"
// @Failure 400 {object} resp.PlayerTeeResp "Bad Request ,param error"
// @Failure 401 {object} resp.PlayerTeeResp "Unauthorized"
// @Failure 404 {object} resp.PlayerTeeResp "Not Found"
// @Failure 500 {object} resp.PlayerTeeResp "Internal Server Error"
func (rec *TeeTime) Personal(c *gin.Context) {

	var req req.TeeTimePersonalReq
	var resp resp.PlayerTeeResp

	requestId := common.GetRequestId(c)

	authInfo, err := repository.GetAppAuthInfo(c)
	if err != nil {
		slog.Error("Failed to get web auth info", "error", err)
		resp.Msg = err.Error()
		resp.Code = http.StatusUnauthorized
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		slog.Error("Failed to bind query", "error", err, "request", requestId)
		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	dateStr := time.Now().Format("20060102")
	officeKey := authInfo.OfficeKey

	// get player from dynamodb
	players, err := playerRepo.GetPlayersFromDynamo(dateStr, officeKey)

	if err != nil {
		slog.Error("PlayerListener.ListenPlayerMessage", "err", err)
		return
	}

	// find player in players by locker no
	var player *player.Player
	for _, p := range players {
		if p.Details.LockerNo == req.LockerNo {
			player = &p
			break
		}
	}

	if player == nil {
		resp.Code = http.StatusNotFound
		resp.Msg = "Player not found"
		c.JSON(resp.Code, resp)
		return
	}

	//find player in players by same cart no
	var otherPlayers []startguidance.OtherPlayer
	for _, p := range players {
		if p.Details.CartNo == player.Details.CartNo && p.Details.PlayerNo != player.Details.PlayerNo {
			otherPlayers = append(otherPlayers, startguidance.OtherPlayer{
				LockerNumber: p.Details.LockerNo,
				PlayerName:   p.Details.PlayerName,
				// if has locker no CheckedIn os true
				CheckedIn: p.Details.LockerNo != "",
			})
		}
	}
	// Get office information once before the loop
	db, err := client.GetMncDBClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "compe list course get db client error",
			"module":    "ListCourse",
			"office_id": authInfo.OfficeID,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	//TODO find course name by player details course index
	courseHandler := courseRepo.CourseRepos{ConnMySQLMncdb: db}
	courses, err := courseHandler.ListCourse(authInfo.OfficeID)
	if err != nil {
		slog.Error("Failed to get courses", "error", err)
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// Find course name by matching player's ScheduledStartCourseIndex with course.CourseIndex
	var courseName string
	for _, course := range courses {
		if course.CourseIndex == player.Details.ScheduledStartCourseIndex {
			courseName = course.CourseName
			break
		}
	}
	// Fallback to first course if no match found
	if courseName == "" && len(courses) > 0 {
		courseName = "未定"
	}

	resp.Code = 200
	resp.Msg = "success"

	resp.Data = &startguidance.PlayerTeeTime{
		CourseName:   courseName,
		CourseIndex:  player.Details.ScheduledStartCourseIndex,
		LockerNo:     player.Details.LockerNo,
		Time:         player.Details.ScheduledStartTime,
		Status:       "",
		CartNo:       player.Details.CartNo,
		PlayerName:   player.Details.PlayerName,
		OtherPlayers: otherPlayers,
	}

	c.JSON(resp.Code, resp)

}

// CartInfo
// @Summary CartInfo (ByLockerNo)
// @Description CartInfo (ByLockerNo)
// @Produce  json
// @Param data body req.CartInfoReq true "Request payload"
// @Success 200 {object} resp.CartInfoResp "response"
// @Failure 400 {object} resp.CartInfoResp "Bad Request ,param error"
// @Failure 401 {object} resp.CartInfoResp "Unauthorized"
// @Failure 404 {object} resp.CartInfoResp "Not Found"
// @Failure 500 {object} resp.CartInfoResp "Internal Server Error"
// @Router /app/start-guidance/cart-info [get]
// @Tags TeeTime
func (rec *TeeTime) CartInfo(c *gin.Context) {
	var req req.CartInfoReq
	var resp resp.CartInfoResp

	requestId := common.GetRequestId(c)

	authInfo, err := repository.GetAppAuthInfo(c)
	if err != nil {
		slog.Error("Failed to get web auth info", "error", err, "request", requestId)
		resp.Msg = err.Error()
		resp.Code = http.StatusUnauthorized
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		slog.Error("Failed to bind query", "error", err, "request", requestId)
		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(http.StatusBadRequest, resp)
		return
	}
	dateStr := time.Now().Format("20060102")
	officeKey := authInfo.OfficeKey

	// get player from dynamodb
	players, err := playerRepo.GetPlayersFromDynamo(dateStr, officeKey)
	if err != nil {
		slog.Error("PlayerListener.ListenPlayerMessage", "err", err)
		return
	}
	// filter players by locker no
	var filteredPlayers []player.Player
	for _, p := range players {
		if p.Details.LockerNo == req.LockerNo {
			filteredPlayers = append(filteredPlayers, p)
		}
	}

	if len(filteredPlayers) == 0 {
		resp.Code = http.StatusNotFound
		resp.Msg = "No players found for this locker number"
		c.JSON(resp.Code, resp)
		return
	}

	if len(filteredPlayers) > 1 {
		slog.Error("Multiple players found for this locker number", "locker_no", req.LockerNo, "players", filteredPlayers)
		resp.Code = http.StatusConflict
		resp.Msg = "Multiple players found for this locker number"
		c.JSON(resp.Code, resp)
		return
	}

	currentPlayer := filteredPlayers[0]

	// Get office information once before the loop
	db, err := client.GetMncDBClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "compe list course get db client error",
			"module":    "ListCourse",
			"office_id": authInfo.OfficeID,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	//TODO find course name by player details course index
	courseHandler := courseRepo.CourseRepos{ConnMySQLMncdb: db}
	courses, err := courseHandler.ListCourse(authInfo.OfficeID)
	if err != nil {
		slog.Error("Failed to get courses", "error", err)
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// Find course name by matching player's ScheduledStartCourseIndex with course.CourseIndex
	var courseName string
	for _, course := range courses {
		if course.CourseIndex == currentPlayer.Details.ScheduledStartCourseIndex {
			courseName = course.CourseName
			break
		}
	}
	// Fallback to first course if no match found
	if courseName == "" && len(courses) > 0 {
		courseName = "未定"
	}

	//find other player with same cart no in players
	var otherPlayers []startguidance.Player
	for _, p := range players {
		if p.Details.CartNo == currentPlayer.Details.CartNo && p.Details.PlayerNo != currentPlayer.Details.PlayerNo {
			otherPlayers = append(otherPlayers, startguidance.Player{
				LockerNo:                  p.Details.LockerNo,
				PlayerNo:                  p.Details.PlayerNo,
				PlayerName:                p.Details.PlayerName,
				ScoreSortKey:              "score_" + dateStr + "_" + p.Details.ScoreHash,
				ScheduledStartCourseIndex: p.Details.ScheduledStartCourseIndex,
				ScheduledStartCourseName:  courseName,
				ScheduledStartTime:        p.Details.ScheduledStartTime,
			})
		}
	}

	// Convert current player to the required format
	var currentCartInfoPlayer startguidance.Player
	currentCartInfoPlayer.LockerNo = currentPlayer.Details.LockerNo
	currentCartInfoPlayer.PlayerNo = currentPlayer.Details.PlayerNo
	currentCartInfoPlayer.PlayerName = currentPlayer.Details.PlayerName
	currentCartInfoPlayer.ScoreSortKey = "score_" + dateStr + "_" + currentPlayer.Details.ScoreHash

	resp.Data = &startguidance.LockerCartInfo{
		CartNo:        currentPlayer.Details.CartNo,
		CurrentPlayer: currentCartInfoPlayer,
		OtherPlayers:  otherPlayers,
	}

	resp.Code = 200
	resp.Msg = "success"

	c.JSON(resp.Code, resp)
}
