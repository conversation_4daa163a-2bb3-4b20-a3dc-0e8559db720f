package v1test

import (
	"mi-restful-api/enum"
	"mi-restful-api/router"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestWebCaddyAnalysis(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/web/caddy?start_month=2024-07&end_month=2024-09&weekday=3&caddy_id=2222", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"data":null,"code":601,"message":"invalid db"}`, w.Body.String())
}

func TestWebStatistical(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/web/statistical?start_month=2024-07&end_month=2024-09&weekday=3", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"data":null,"code":601,"message":"invalid db"}`, w.Body.String())
}
