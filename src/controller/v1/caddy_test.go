package v1_test

import (
	"mi-restful-api/enum"
	"mi-restful-api/router"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAppCaddyOn(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/app/caddyon", nil)
	req.Header.Set("token", enum.MockAuthToken)
	req.Header.Set("device-id", enum.MockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"code":601,"message":"invalid db","caddyon":""}`, w.Body.String())
}

func TestWebCaddyList(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/web/caddylist", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"code":601,"message":"invalid db","data":null}`, w.Body.String())
}
