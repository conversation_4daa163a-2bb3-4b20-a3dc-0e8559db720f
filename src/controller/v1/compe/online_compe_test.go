package v1compe_test

import (
	"bytes"
	"encoding/json"
	v1compe "mi-restful-api/controller/v1/compe"
	"mi-restful-api/model/compe"
	request "mi-restful-api/request/compe"
	compeResp "mi-restful-api/response/compe"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	return router
}

func setupMockContext(method, path string, body interface{}) (*gin.Context, *httptest.ResponseRecorder) {
	w := httptest.NewRecorder()
	var req *http.Request

	if body != nil {
		jsonBody, _ := json.Marshal(body)
		req = httptest.NewRequest(method, path, bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req = httptest.NewRequest(method, path, nil)
	}

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up mock auth info
	c.Set("request_id", "test-request-id")

	return c, w
}

// Test controller structure initialization
func TestOnlineCompeController_Initialization(t *testing.T) {
	controller := &v1compe.OnlineCompe{}
	assert.NotNil(t, controller)
}

// Test UploadImg parameter validation
func TestUploadImg_MissingCompeNo(t *testing.T) {
	// Create test context without compeNo parameter
	c, w := setupMockContext("POST", "/web/online-compe/img/upload/", nil)

	// Execute
	controller := &v1compe.OnlineCompe{}
	controller.UploadImg(c)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response compeResp.UploadImgResp
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, response.Code)
	assert.Equal(t, "compe ID is required", response.Msg)
}

// Test UploadImg with invalid compeNo
func TestUploadImg_InvalidCompeNo(t *testing.T) {
	// Create test context with invalid compeNo parameter
	c, w := setupMockContext("POST", "/web/online-compe/img/upload/invalid", nil)
	c.Params = gin.Params{{Key: "compeNo", Value: "invalid"}}

	// Execute
	controller := &v1compe.OnlineCompe{}
	controller.UploadImg(c)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response compeResp.UploadImgResp
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, response.Code)
	assert.Contains(t, response.Msg, "failed to get file from request")
}

// Test UploadImg with valid compeNo but missing file
func TestUploadImg_MissingFile(t *testing.T) {
	// Create test context with valid compeNo but no file
	c, w := setupMockContext("POST", "/web/online-compe/img/upload/1", nil)
	c.Params = gin.Params{{Key: "compeNo", Value: "1"}}

	// Execute
	controller := &v1compe.OnlineCompe{}
	controller.UploadImg(c)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response compeResp.UploadImgResp
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, response.Code)
	assert.Contains(t, response.Msg, "failed to get file from request")
}

// Test Create request structure validation
func TestCreate_RequestStructure(t *testing.T) {
	// Create test request with valid structure
	entryFromNavi := 1
	rankingAggregation := 0
	compeType := 1
	markerSetting := 0

	req := request.OnlineCompeCreationReq{
		Basic: compe.Basic{
			CompeNo:   6,
			CompeName: "Test Competition",
			Duration: struct {
				From time.Time `json:"from"  binding:"required" dynamodbav:"from"`
				To   time.Time `json:"to" binding:"required,gtfield=From,gt" dynamodbav:"to"`
			}{
				From: time.Now(),
				To:   time.Now().Add(24 * time.Hour),
			},
			Organizer: "Test Organizer",
		},
		CompeSetting: compe.CompeSetting{
			EntryFromNavi:      &entryFromNavi,
			RankingAggregation: &rankingAggregation,
			Round:              "1",
		},
		CompeTypeSetting: compe.CompeTypeSetting{
			Type: &compeType,
		},
		OtherSetting: compe.OtherSetting{
			MarkerSetting: &markerSetting,
		},
		PrivateSetting: &compe.PrivateSetting{
			CourseSetting: map[string]string{"course1": "Course 1"},
			HiddenHole:    []compe.HiddenHoleSetting{},
		},
	}

	// Test that the request structure is valid
	assert.Equal(t, 6, req.Basic.CompeNo)
	assert.Equal(t, "Test Competition", req.Basic.CompeName)
	assert.Equal(t, "Test Organizer", req.Basic.Organizer)
	assert.Equal(t, 1, *req.CompeSetting.EntryFromNavi)
	assert.Equal(t, 0, *req.CompeSetting.RankingAggregation)
	assert.Equal(t, "1", req.CompeSetting.Round)
	assert.Equal(t, 1, *req.CompeTypeSetting.Type)
	assert.Equal(t, 0, *req.OtherSetting.MarkerSetting)
	assert.NotNil(t, req.PrivateSetting)
	assert.Equal(t, "Course 1", req.PrivateSetting.CourseSetting["course1"])
	assert.Equal(t, 0, len(req.PrivateSetting.HiddenHole))

	// Test that Duration is properly set
	assert.True(t, req.Basic.Duration.To.After(req.Basic.Duration.From))
}

// Test Update request structure validation
func TestUpdate_RequestStructure(t *testing.T) {
	// Create test request with valid structure for update
	entryFromNavi := 1
	compeType := 1
	markerSetting := 0

	req := request.OnlineCompeCreationReq{
		Basic: compe.Basic{
			CompeNo:   1,
			CompeName: "Updated Competition",
			Duration: struct {
				From time.Time `json:"from"  binding:"required" dynamodbav:"from"`
				To   time.Time `json:"to" binding:"required,gtfield=From,gt" dynamodbav:"to"`
			}{
				From: time.Now(),
				To:   time.Now().Add(24 * time.Hour),
			},
		},
		CompeSetting: compe.CompeSetting{
			EntryFromNavi: &entryFromNavi,
		},
		CompeTypeSetting: compe.CompeTypeSetting{
			Type: &compeType,
		},
		OtherSetting: compe.OtherSetting{
			MarkerSetting: &markerSetting,
		},
		PrivateSetting: &compe.PrivateSetting{
			CourseSetting: map[string]string{"course1": "Updated Course"},
			HiddenHole:    []compe.HiddenHoleSetting{},
		},
	}

	// Test that the request structure is valid for update
	assert.Equal(t, 1, req.Basic.CompeNo)
	assert.Equal(t, "Updated Competition", req.Basic.CompeName)
	assert.Equal(t, 1, *req.CompeSetting.EntryFromNavi)
	assert.Equal(t, 1, *req.CompeTypeSetting.Type)
	assert.Equal(t, 0, *req.OtherSetting.MarkerSetting)
	assert.Equal(t, "Updated Course", req.PrivateSetting.CourseSetting["course1"])
}

// Test OnlineCompe parameter validation
func TestOnlineCompe_ParameterValidation(t *testing.T) {
	// Test with valid compeNo parameter
	c, _ := setupMockContext("GET", "/web/online-compe/1", nil)
	c.Params = gin.Params{{Key: "compeNo", Value: "1"}}

	compeNoStr := c.Param("compeNo")
	compeNo, err := strconv.Atoi(compeNoStr)

	assert.NoError(t, err)
	assert.Equal(t, 1, compeNo)

	// Test with invalid compeNo parameter
	c2, _ := setupMockContext("GET", "/web/online-compe/invalid", nil)
	c2.Params = gin.Params{{Key: "compeNo", Value: "invalid"}}

	compeNoStr2 := c2.Param("compeNo")
	_, err2 := strconv.Atoi(compeNoStr2)

	assert.Error(t, err2)
}

// Test UpdateDefaultSetting request structure
func TestUpdateDefaultSetting_RequestStructure(t *testing.T) {
	req := request.OnlineCompeDefaultSettingReq{
		CompeSetting: compe.DefaultCompeSetting{
			EntryFromNavi:      1,
			RankingAggregation: 0,
			Round:              "1",
		},
		OtherSetting: compe.DefaultOtherSetting{
			MarkerSetting: 0,
		},
		Handicap: compe.DefaultHandicap{
			Type: 0,
		},
	}

	// Test that the request structure is valid
	assert.Equal(t, 1, req.CompeSetting.EntryFromNavi)
	assert.Equal(t, 0, req.CompeSetting.RankingAggregation)
	assert.Equal(t, "1", req.CompeSetting.Round)
	assert.Equal(t, 0, req.OtherSetting.MarkerSetting)
	assert.Equal(t, 0, req.Handicap.Type)
}

// Test JoinCompe request structure
func TestJoinCompe_RequestStructure(t *testing.T) {
	req := request.OnlineCompePlayerJoinReq{
		PlayerNo:   123,
		PlayerName: "Test Player",
		CompeNo:    1,
		OfficeKey:  "test_office",
		PlayDate:   time.Now().Format("20060102"),
		CartNo:     1,
		IsPaid:     true,
	}

	// Test that the request structure is valid
	assert.Equal(t, 123, req.PlayerNo)
	assert.Equal(t, "Test Player", req.PlayerName)
	assert.Equal(t, 1, req.CompeNo)
	assert.Equal(t, "test_office", req.OfficeKey)
	assert.Equal(t, 1, req.CartNo)
	assert.True(t, req.IsPaid)

	// Test PlayDate format
	_, err := time.Parse("20060102", req.PlayDate)
	assert.NoError(t, err)
}

// Test response structure creation
func TestResponseStructures(t *testing.T) {
	// Test BasicResp
	basicResp := compeResp.BasicResp{
		Code: http.StatusOK,
		Msg:  "success",
	}
	assert.Equal(t, http.StatusOK, basicResp.Code)
	assert.Equal(t, "success", basicResp.Msg)

	// Test UploadImgResp
	uploadResp := compeResp.UploadImgResp{
		Code: http.StatusOK,
		Msg:  "success",
		Data: struct {
			Url string `json:"url"`
		}{
			Url: "https://example.com/image.jpg",
		},
	}
	assert.Equal(t, http.StatusOK, uploadResp.Code)
	assert.Equal(t, "success", uploadResp.Msg)
	assert.Equal(t, "https://example.com/image.jpg", uploadResp.Data.Url)

	// Test OnlineCompeLatestNoResp
	latestNoResp := compeResp.OnlineCompeLatestNoResp{
		Code: http.StatusOK,
		Msg:  "success",
		Data: struct {
			CompeNo int `json:"compe_no"`
		}{
			CompeNo: 6,
		},
	}
	assert.Equal(t, http.StatusOK, latestNoResp.Code)
	assert.Equal(t, "success", latestNoResp.Msg)
	assert.Equal(t, 6, latestNoResp.Data.CompeNo)
}

// Test query parameter parsing for ListOfficeCompe
func TestListOfficeCompe_QueryParameters(t *testing.T) {
	// Test with valid query parameters
	c, _ := setupMockContext("GET", "/web/online-compe/office-compe/list?offset=0&limit=100", nil)
	c.Request.URL.RawQuery = "offset=0&limit=100"

	// Test parameter extraction
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "100")

	offset, err1 := strconv.Atoi(offsetStr)
	limit, err2 := strconv.Atoi(limitStr)

	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.Equal(t, 0, offset)
	assert.Equal(t, 100, limit)

	// Test with missing parameters (should use defaults)
	c2, _ := setupMockContext("GET", "/web/online-compe/office-compe/list", nil)

	offsetStr2 := c2.DefaultQuery("offset", "0")
	limitStr2 := c2.DefaultQuery("limit", "100")

	offset2, err3 := strconv.Atoi(offsetStr2)
	limit2, err4 := strconv.Atoi(limitStr2)

	assert.NoError(t, err3)
	assert.NoError(t, err4)
	assert.Equal(t, 0, offset2)
	assert.Equal(t, 100, limit2)
}

// Test compe data structures
func TestCompeDataStructures(t *testing.T) {
	// Test Basic structure
	basic := compe.Basic{
		CompeNo:   1,
		CompeName: "Test Competition",
		Duration: struct {
			From time.Time `json:"from"  binding:"required" dynamodbav:"from"`
			To   time.Time `json:"to" binding:"required,gtfield=From,gt" dynamodbav:"to"`
		}{
			From: time.Now(),
			To:   time.Now().Add(24 * time.Hour),
		},
		Organizer: "Test Organizer",
	}

	assert.Equal(t, 1, basic.CompeNo)
	assert.Equal(t, "Test Competition", basic.CompeName)
	assert.Equal(t, "Test Organizer", basic.Organizer)
	assert.True(t, basic.Duration.To.After(basic.Duration.From))

	// Test PrivateSetting structure
	privateSetting := compe.PrivateSetting{
		CourseSetting: map[string]string{
			"course1": "Course 1",
			"course2": "Course 2",
		},
		HiddenHole: []compe.HiddenHoleSetting{
			{
				CourseName:      "Test Course",
				CourseIndex:     "1",
				HiddenHoleIndex: []int{1, 2, 3},
			},
		},
	}

	assert.Equal(t, 2, len(privateSetting.CourseSetting))
	assert.Equal(t, "Course 1", privateSetting.CourseSetting["course1"])
	assert.Equal(t, "Course 2", privateSetting.CourseSetting["course2"])
	assert.Equal(t, 1, len(privateSetting.HiddenHole))
	assert.Equal(t, "Test Course", privateSetting.HiddenHole[0].CourseName)
	assert.Equal(t, "1", privateSetting.HiddenHole[0].CourseIndex)
	assert.Equal(t, 3, len(privateSetting.HiddenHole[0].HiddenHoleIndex))
	assert.Equal(t, []int{1, 2, 3}, privateSetting.HiddenHole[0].HiddenHoleIndex)
}

// Test HTTP context setup and parameter extraction
func TestHTTPContextHandling(t *testing.T) {
	// Test JSON body parsing
	testData := map[string]interface{}{
		"compe_no":   1,
		"compe_name": "Test Competition",
		"organizer":  "Test Organizer",
	}

	c, _ := setupMockContext("POST", "/test", testData)

	// Test that request has correct content type
	assert.Equal(t, "application/json", c.GetHeader("Content-Type"))

	// Test that request_id is set
	requestID, exists := c.Get("request_id")
	assert.True(t, exists)
	assert.Equal(t, "test-request-id", requestID)

	// Test parameter extraction from URL path
	c.Params = gin.Params{
		{Key: "compeNo", Value: "123"},
		{Key: "officeKey", Value: "test_office"},
	}

	assert.Equal(t, "123", c.Param("compeNo"))
	assert.Equal(t, "test_office", c.Param("officeKey"))
	assert.Equal(t, "", c.Param("nonexistent"))
}

// Test error response structures
func TestErrorResponseStructures(t *testing.T) {
	// Test error response for UploadImg
	errorResp := compeResp.UploadImgResp{
		Code: http.StatusBadRequest,
		Msg:  "compe ID is required",
		Data: struct {
			Url string `json:"url"`
		}{
			Url: "",
		},
	}

	assert.Equal(t, http.StatusBadRequest, errorResp.Code)
	assert.Equal(t, "compe ID is required", errorResp.Msg)
	assert.Equal(t, "", errorResp.Data.Url)

	// Test error response for BasicResp
	basicErrorResp := compeResp.BasicResp{
		Code: http.StatusInternalServerError,
		Msg:  "internal server error",
	}

	assert.Equal(t, http.StatusInternalServerError, basicErrorResp.Code)
	assert.Equal(t, "internal server error", basicErrorResp.Msg)
}

// Test time formatting and parsing
func TestTimeHandling(t *testing.T) {
	now := time.Now()

	// Test date format for PlayDate (YYYYMMDD)
	playDate := now.Format("20060102")
	parsedDate, err := time.Parse("20060102", playDate)

	assert.NoError(t, err)
	assert.Equal(t, now.Year(), parsedDate.Year())
	assert.Equal(t, now.Month(), parsedDate.Month())
	assert.Equal(t, now.Day(), parsedDate.Day())

	// Test RFC3339 format for timestamps
	timestamp := now.Format(time.RFC3339)
	parsedTimestamp, err := time.Parse(time.RFC3339, timestamp)

	assert.NoError(t, err)
	assert.True(t, parsedTimestamp.Equal(now.Truncate(time.Second)))
}
