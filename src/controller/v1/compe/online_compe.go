package v1compe

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model/compe"
	"mi-restful-api/model/player"
	"mi-restful-api/model/tee"
	"mi-restful-api/repository"
	request "mi-restful-api/request/compe"
	teeReq "mi-restful-api/request/tee"
	compeResp "mi-restful-api/response/compe"
	courseResp "mi-restful-api/response/course"
	playerResp "mi-restful-api/response/player"
	teeResp "mi-restful-api/response/tee"
	"mi-restful-api/utils"
	"mi-restful-api/utils/common"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	compeRepo "mi-restful-api/repository/compe"
	courseRepo "mi-restful-api/repository/course"
	playerRepo "mi-restful-api/repository/player"
	teeRepo "mi-restful-api/repository/tee"
)

type OnlineCompe struct {
}

// GetLatestCompeNo
// @Summary get latest avaliable compe id
// @Description get latest avaliable compe id
// @Produce  json
// @Success 200 {object} compeResp.OnlineCompeLatestNoResp "response"
// @Failure 400 {object} compeResp.OnlineCompeLatestNoResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.OnlineCompeLatestNoResp "Unauthorized"
// @Failure 404 {object} compeResp.OnlineCompeLatestNoResp "Not Found"
// @Failure 500 {object} compeResp.OnlineCompeLatestNoResp "Internal Server Error"
// @Router /web/online-compe/latest-no [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) GetLatestNo(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp compeResp.OnlineCompeLatestNoResp
	resp.Data.CompeNo = 0

	lastNo, err := compeRepo.OnlineCompeLastNo()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get compe last no error",
			"module":   "GetLatestId",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data.CompeNo = lastNo + 1
	c.JSON(resp.Code, resp)
}

// UploadImg
// @Summary upload img to s3
// @Description upload img to s3
// @Produce json
// @Success 200 {object} compeResp.UploadImgResp "response"
// @Failure 400 {object} compeResp.UploadImgResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.UploadImgResp "Unauthorized"
// @Failure 404 {object} compeResp.UploadImgResp "Not Found"
// @Failure 500 {object} compeResp.UploadImgResp "Internal Server Error"
// @Router /web/online-compe/img/upload/:compeNo [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) UploadImg(c *gin.Context) {
	maxUploadSize := int64(10 * 1024 * 1024)
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxUploadSize)

	var resp compeResp.UploadImgResp
	requestId := common.GetRequestId(c)
	// Get compeNo from request parameter
	compeNo := c.Param("compeNo")
	if compeNo == "" {
		resp := compeResp.UploadImgResp{
			Code: http.StatusBadRequest,
			Msg:  "compe ID is required",
		}
		c.JSON(resp.Code, resp)
		return
	}

	// Get file from request
	file, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "Failed to get file from request",
			"module":   "UploadImg",
			"request":  requestId,
			"err":      err,
		})

		resp := compeResp.UploadImgResp{
			Code: http.StatusBadRequest,
			Msg:  "failed to get file from request",
		}
		c.JSON(resp.Code, resp)
		return
	}
	defer file.Close()

	// check file size
	if fileHeader.Size > maxUploadSize {
		resp := compeResp.UploadImgResp{
			Code: http.StatusBadRequest,
			Msg:  "File too large",
		}
		c.JSON(resp.Code, resp)
		return
	}

	// Validate file type
	contentType := fileHeader.Header.Get("Content-Type")
	if !common.IsImageContentType(contentType) {
		resp := compeResp.UploadImgResp{
			Code: http.StatusBadRequest,
			Msg:  "invalid file type, only images are allowed",
		}
		c.JSON(resp.Code, resp)
		return
	}

	filename := common.GenerateUniqueFilename(fileHeader.Filename)
	s3Key := configs.GetAwsS3Config().S3PromotionalPrefix + compeNo + "/tmp/" + filename

	// Upload file to S3 tmp directory
	s3Client := common.NewS3Client()
	fileURL, err := s3Client.UploadFile(file, s3Key, contentType)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "Failed to upload file to S3",
			"module":   "UploadImg",
			"request":  requestId,
			"err":      err,
		})

		resp := compeResp.UploadImgResp{
			Code: http.StatusInternalServerError,
			Msg:  "failed to upload file",
			Data: struct {
				Url string "json:\"url\""
			}{
				Url: fileURL,
			},
		}
		c.JSON(resp.Code, resp)
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data.Url = fileURL

	logging.LogFormat(enum.LogWarn, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "upload file params response log",
		"module":   "UploadImg",
		"request":  requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}

// Create
// @Summary create compe
// @Description create compe
// @Produce  json
// @Param data body request.OnlineCompeCreationReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/create [put]
// @Tags OnlineCompe
func (rec *OnlineCompe) Create(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "Create",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Info("Create: ", "officeId", authInfo.OfficeID)

	var resp compeResp.BasicResp
	var req request.OnlineCompeCreationReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe create params parse error",
			"module":   "Create",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "PUT",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	// validate req
	latestId, err := compeRepo.OnlineCompeLastNo()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe create params parse error",
			"module":   "Create",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "PUT",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	if req.Basic.CompeNo <= latestId {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe create params parse error",
			"module":   "Create",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "PUT",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = "compe no error"
		c.JSON(resp.Code, resp)
		return
	}

	newBase := req.Basic
	s3Client := common.NewS3Client()
	url, err := s3Client.MoveFile(strconv.Itoa(req.Basic.CompeNo))
	if err == nil {
		newBase.PromotionalImage = &url
	}

	onlineCompe := compe.OnlineCompe{
		PartitionKey: "#online-compe-basic",
		SortKey:      "online-compe_" + strconv.Itoa(req.Basic.CompeNo),
		Details: struct {
			Basic            compe.Basic            `json:"basic" dynamodbav:"basic"`
			CompeSetting     compe.CompeSetting     `json:"compe_setting" dynamodbav:"compe_setting"`
			CompeTypeSetting compe.CompeTypeSetting `json:"compe_type_setting" dynamodbav:"compe_type_setting"`
			OtherSetting     compe.OtherSetting     `json:"other_setting" dynamodbav:"other_setting"`
		}{
			Basic:            newBase,
			CompeSetting:     req.CompeSetting,
			CompeTypeSetting: req.CompeTypeSetting,
			OtherSetting:     req.OtherSetting,
		},
		ExpirationTime: 0, // in seconds
		UpdatedAt:      time.Now(),
	}
	_, err = compeRepo.CreateOnlineCompe(onlineCompe)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "compe create params db error",
			"module":    "Create",
			"office_id": authInfo.OfficeID,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "PUT",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(resp.Code, resp)
		return
	}

	if req.PrivateSetting != nil {
		officeCompe := compe.OfficeCompe{
			OfficeKey: authInfo.OfficeKey,
			SortKey:   "online-compe_" + strconv.Itoa(req.Basic.CompeNo), //compe_${compe_no}
			Details: compe.OfficeCompeDetails{
				CompeNo:              req.Basic.CompeNo,
				CompeName:            req.Basic.CompeName,
				CourseSetting:        req.PrivateSetting.CourseSetting,
				PrizeConditionSetted: false,
				HiddenHole:           req.PrivateSetting.HiddenHole,
				SharedKey:            "",
			},
			ExpirationTime: 0, // in seconds
			UpdatedAt:      time.Now(),
		}
		_, err = compeRepo.UpsertOfficeToCompe(officeCompe, req.Basic.Duration.To)
		if err != nil {
			logging.LogFormat(enum.LogError, map[string]any{
				"category":  enum.LogCategoryApp,
				"message":   "compe create params private setting db error",
				"module":    "Create",
				"office_id": authInfo.OfficeID,
				"request":   requestId,
				"err":       err,
				"param": map[string]any{
					"req": map[string]any{
						"method": "PUT",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Msg = err.Error()
			resp.Code = exception.StatusCode(err)
			c.JSON(resp.Code, resp)
			return
		}
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "compe create params response log",
		"module":    "Create",
		"office_id": authInfo.OfficeID,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	resp.Code = http.StatusOK
	resp.Msg = "success"
	c.JSON(resp.Code, resp)
}

// Update
// @Summary update compe
// @Description update compe
// @Produce  json
// @Param data body request.OnlineCompeUpdateReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/update [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) Update(c *gin.Context) {
	requestId := common.GetRequestId(c)

	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "ListCourse",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Debug("debug officeId :", "officeId", authInfo.OfficeID)

	var resp compeResp.BasicResp
	var req request.OnlineCompeCreationReq

	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe update params parse error",
			"module":   "Update",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	// TODO: validate req

	// TODO: differ data
	onlineCompe, err := compeRepo.OnlineCompeByNo(req.Basic.CompeNo)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe update params parse error",
			"module":   "Update",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusNotFound
		resp.Msg = "compe not found"
		c.JSON(resp.Code, resp)
		return
	}

	newBase := req.Basic
	if newBase.PromotionalImage != onlineCompe.Details.Basic.PromotionalImage && onlineCompe.Details.Basic.PromotionalImage != nil {
		s3Client := common.NewS3Client()
		_ = s3Client.ClearFile(*onlineCompe.Details.Basic.PromotionalImage)
		url, err := s3Client.MoveFile(strconv.Itoa(newBase.CompeNo))
		if err == nil {
			newBase.PromotionalImage = &url
		}
	}
	onlineCompe = compe.OnlineCompe{
		PartitionKey: "#online-compe-basic",
		SortKey:      "online-compe_" + strconv.Itoa(req.Basic.CompeNo),
		Details: struct {
			Basic            compe.Basic            `json:"basic" dynamodbav:"basic"`
			CompeSetting     compe.CompeSetting     `json:"compe_setting" dynamodbav:"compe_setting"`
			CompeTypeSetting compe.CompeTypeSetting `json:"compe_type_setting" dynamodbav:"compe_type_setting"`
			OtherSetting     compe.OtherSetting     `json:"other_setting" dynamodbav:"other_setting"`
		}{
			Basic:            newBase,
			CompeSetting:     req.CompeSetting,
			CompeTypeSetting: req.CompeTypeSetting,
			OtherSetting:     req.OtherSetting,
		},
		ExpirationTime: 0, // in seconds
		UpdatedAt:      time.Now(),
	}
	_, err = compeRepo.UpdateOnlineCompe(onlineCompe)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "compe update params db error",
			"module":    "Update",
			"office_id": authInfo.OfficeID,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(resp.Code, resp)
		return
	}

	if req.PrivateSetting != nil {
		officeCompe := compe.OfficeCompe{
			OfficeKey: authInfo.OfficeKey,
			SortKey:   "online-compe_" + strconv.Itoa(req.Basic.CompeNo), //compe_${compe_no}
			Details: compe.OfficeCompeDetails{
				CompeNo:              req.Basic.CompeNo,
				CompeName:            req.Basic.CompeName,
				CourseSetting:        req.PrivateSetting.CourseSetting,
				PrizeConditionSetted: true,
				HiddenHole:           req.PrivateSetting.HiddenHole,
				SharedKey:            "",
			},
			ExpirationTime: 0, // in seconds
			UpdatedAt:      time.Now(),
		}

		_, err = compeRepo.UpsertOfficeToCompe(officeCompe, req.Basic.Duration.To)
		if err != nil {
			logging.LogFormat(enum.LogError, map[string]any{
				"category":  enum.LogCategoryApp,
				"message":   "compe update params private setting db error",
				"module":    "Update",
				"office_id": authInfo.OfficeID,
				"request":   requestId,
				"err":       err,
				"param": map[string]any{
					"req": map[string]any{
						"method": "POST",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Msg = err.Error()
			resp.Code = exception.StatusCode(err)
			c.JSON(resp.Code, resp)
			return
		}
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "compe update params response log",
		"module":    "Update",
		"office_id": authInfo.OfficeID,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	resp.Code = http.StatusOK
	resp.Msg = "success"
	c.JSON(resp.Code, resp)
}

// OnlineCompe
// @Summary get compe by no
// @Description get compe by no
// @Produce  json
// @Success 200 {object} compeResp.OnlineCompeResp "response"
// @Failure 400 {object} compeResp.OnlineCompeResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.OnlineCompeResp "Unauthorized"
// @Failure 404 {object} compeResp.OnlineCompeResp "Not Found"
// @Failure 500 {object} compeResp.OnlineCompeResp "Internal Server Error"
// @Router /web/online-compe/:compeNo [get]
// @Router /app/online-compe/:compeNo [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) OnlineCompe(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp compeResp.OnlineCompeResp

	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get online compe id error",
			"module":   "OnlineCompe",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	compeNo := c.Param("compeNo")
	slog.Debug("debug request:", "compeNo", compeNo)

	numId, err := strconv.ParseInt(compeNo, 10, 64)
	if err != nil {
		slog.Error("error :", "err", err)
	}

	// common
	onlineCompe, err := compeRepo.OnlineCompeByNo(int(numId))
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get online compe by no error",
			"module":   "OnlineCompe",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	resp.Data.Basic = onlineCompe.Details.Basic
	resp.Data.CompeSetting = onlineCompe.Details.CompeSetting
	resp.Data.CompeTypeSetting = onlineCompe.Details.CompeTypeSetting
	resp.Data.OtherSetting = onlineCompe.Details.OtherSetting

	// private setting
	sk := "online-compe_" + strconv.Itoa(int(numId))
	officeCompe, err := compeRepo.OnlineCompePrivateByNo(authInfo.OfficeKey, sk)
	if err == nil {
		resp.Data.PrivateSetting = compe.PrivateSetting{
			CourseSetting: officeCompe.CourseSetting,
			HiddenHole:    officeCompe.HiddenHole,
		}
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"

	c.JSON(resp.Code, resp)
}

// UpdateDefaultSetting
// @Summary update compe default setting
// @Description update compe default setting
// @Produce  json
// @Param data body request.OnlineCompeDefaultSettingReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/default-setting/update [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) UpdateDefaultSetting(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe defult setting update get office id error",
			"module":   "UpdateDefaultSetting",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Info("Create: ", "officeId", authInfo.OfficeID)

	var resp compeResp.BasicResp
	var req request.OnlineCompeDefaultSettingReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe defult setting update params parse error",
			"module":   "UpdateDefaultSetting",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "PUT",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	defaultSetting := compe.OnlineCompeDefaultSetting{
		PartitionKey: "#online-compe-default",
		SortKey:      "setting_" + authInfo.OfficeKey,
		Details: struct {
			CompeSetting compe.DefaultCompeSetting `json:"compe_setting" dynamodbav:"compe_setting"`
			OtherSetting compe.DefaultOtherSetting `json:"other_setting" dynamodbav:"other_setting"`
			Handicap     compe.DefaultHandicap     `json:"handicap" dynamodbav:"handicap"`
		}{
			CompeSetting: req.CompeSetting,
			OtherSetting: req.OtherSetting,
			Handicap:     req.Handicap,
		},
		ExpirationTime: 0, // in seconds
		UpdatedAt:      time.Now(),
	}
	_, err = compeRepo.UpdateDefaultOnlineCompeSetting(defaultSetting)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe defult setting update error",
			"module":   "UpdateDefaultSetting",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "compe defult setting update response log",
		"module":    "UpdateDefaultSetting",
		"office_id": authInfo.OfficeID,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	resp.Code = http.StatusOK
	resp.Msg = "success"
	c.JSON(resp.Code, resp)
}

// DefaultSetting
// @Summary get compe default setting
// @Description get compe default setting
// @Produce  json
// @Success 200 {object} compeResp.OnlineCompeDefaultSettingResp "response"
// @Failure 400 {object} compeResp.OnlineCompeDefaultSettingResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.OnlineCompeDefaultSettingResp "Unauthorized"
// @Failure 404 {object} compeResp.OnlineCompeDefaultSettingResp "Not Found"
// @Failure 500 {object} compeResp.OnlineCompeDefaultSettingResp "Internal Server Error"
// @Router /web/online-compe/default-setting [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) DefaultSetting(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp compeResp.OnlineCompeDefaultSettingResp
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get default setting params error",
			"module":   "DefaultSetting",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Debug("debug officeId :", "officeId", authInfo.OfficeID)

	setting, err := compeRepo.OnlineCompeDefaultSetting(authInfo.OfficeKey)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get default setting error",
			"module":   "DefaultSetting",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	if setting == nil {
		logging.LogFormat(enum.LogWarn, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get default setting no data",
			"module":   "DefaultSetting",
			"request":  requestId,
		})
		resp.Code = http.StatusNotFound
		resp.Msg = "no data"
		c.JSON(resp.Code, resp)
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data = struct {
		compe.DefaultCompeSetting `json:"compe_setting"`
		compe.DefaultOtherSetting `json:"other_setting"`
		compe.DefaultHandicap     `json:"handicap"`
	}{
		DefaultCompeSetting: setting.Details.CompeSetting,
		DefaultOtherSetting: setting.Details.OtherSetting,
		DefaultHandicap:     setting.Details.Handicap,
	}
	c.JSON(resp.Code, resp)
}

// JoinCompe
// @Summary JoinCompe compe
// @Description JoinCompe compe
// @Produce  json
// @Param data body request.OnlineCompePlayerJoinReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/join [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) JoinCompe(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp compeResp.BasicResp
	var req request.OnlineCompePlayerJoinReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "compe join params parse error",
			"module":   "JoinCompe",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "PUT",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	slog.Info("JoinCompe:", "req", req)
	// TODO: validate req

	glidNo := ""
	if req.GlidNo != nil {
		glidNo = *req.GlidNo
	}

	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		slog.Error("get auth info error :", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	onlineCompe, err := compeRepo.OnlineCompeByNo(req.CompeNo)
	if err != nil {
		slog.Error("get online compe error :", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	// Validate that req.PlayDate is within the competition's duration
	playDate, err := time.Parse("20060102", req.PlayDate)
	if err != nil {
		slog.Error("Invalid play date format:", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = "Invalid play date format"
		c.JSON(resp.Code, resp)
		return
	}

	// Set time to midnight for date comparison
	playDate = time.Date(playDate.Year(), playDate.Month(), playDate.Day(), 0, 0, 0, 0, playDate.Location())
	fromDate := time.Date(onlineCompe.Details.Basic.Duration.From.Year(), onlineCompe.Details.Basic.Duration.From.Month(), onlineCompe.Details.Basic.Duration.From.Day(), 0, 0, 0, 0, onlineCompe.Details.Basic.Duration.From.Location())
	toDate := time.Date(onlineCompe.Details.Basic.Duration.To.Year(), onlineCompe.Details.Basic.Duration.To.Month(), onlineCompe.Details.Basic.Duration.To.Day(), 23, 59, 59, 0, onlineCompe.Details.Basic.Duration.To.Location())

	if playDate.Before(fromDate) || playDate.After(toDate) {
		slog.Error("Play date outside competition duration:", "playDate", playDate, "from", fromDate, "to", toDate)
		resp.Code = http.StatusBadRequest
		resp.Msg = "Play date must be within competition duration"
		c.JSON(resp.Code, resp)
		return
	}

	officeKey := authInfo.OfficeKey
	if req.OfficeKey != "" {
		officeKey = req.OfficeKey
	}
	cp := compe.CompePlayer{
		PartitionKey: "online-compe_" + strconv.Itoa(req.CompeNo),
		SortKey: "online-compe_player_" + officeKey +
			"_" + req.PlayDate +
			"_" + strconv.Itoa(req.CartNo) +
			"_" + strconv.Itoa(req.PlayerNo),
		Details: compe.CompePlayerDetails{
			CompeNo:       req.CompeNo,
			OfficeKey:     officeKey,
			PlayerNo:      req.PlayerNo,
			PlayerName:    req.PlayerName,
			Birthday:      req.Birthday,
			Gender:        req.Gender,
			GlidNo:        glidNo,
			TeeId:         req.TeeId,
			Hdcp:          req.Hdcp,
			HdcpIndex:     req.HdcpIndex,
			PlayDate:      req.PlayDate,
			CartNo:        req.CartNo,
			CourseIndex:   req.CourseIndex,
			TeamClassType: req.TeamClassType,
			IsPaid:        req.IsPaid,
		},
		ExpirationTime: 0, // in seconds
		UpdatedAt:      time.Now(),
	}

	handy := onlineCompe.Details.CompeTypeSetting.Handy
	if handy != nil &&
		handy.Handicap.Type != nil &&
		*handy.Handicap.Type != 1 &&
		cp.Details.HdcpIndex != nil &&
		cp.Details.Gender != nil &&
		*cp.Details.HdcpIndex != "" {
		courseRatingResult, err := playerRepo.SearchPlayerCourseRateByInteralApi(authInfo.OfficeID, cp.Details)
		if err != nil {
			slog.Warn("SearchPlayerCourseRateByInteralApi error :", "err", err, "player", cp.Details, "officeId", authInfo.OfficeID)
			courseRatingResult = &playerResp.CourseRatingResult{}
		} else {
			var playingHdcp string
			if *cp.Details.Gender == 1 {
				playingHdcp = utils.ComputeHdcp(*cp.Details.HdcpIndex, courseRatingResult.MenSlopeRating, courseRatingResult.MenCourseRating, utils.ComputePar(courseRatingResult.MenPar), handy.Handicap.HdcpAllowance)
			} else {
				playingHdcp = utils.ComputeHdcp(*cp.Details.HdcpIndex, courseRatingResult.WomenSlopeRating, courseRatingResult.WomenCourseRating, utils.ComputePar(courseRatingResult.WomenPar), handy.Handicap.HdcpAllowance)
			}

			slog.Info("compute hdcp", "playingHdcp", playingHdcp, "courseRatingResult", courseRatingResult, "compePlayer", cp)
			cp.Details.PlayingHdcp = &playingHdcp
		}
	}

	// TODO: insert into db  trancation start
	result, err := compeRepo.UpsertCompeToPlayer(cp)
	if result && err != nil {
		slog.Error("UpsertCompeToPlayer error :", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	playerCompe := player.PlayerCompe{
		PartitionKey: req.OfficeKey,
		SortKey: "player_online-compe_" + strconv.Itoa(req.PlayerNo) +
			"_" + strconv.Itoa(req.CompeNo) +
			"_" + strconv.Itoa(req.CartNo) +
			"_" + req.PlayDate,
		Details: player.PlayerCompeDetails{
			PlayerNo:    req.PlayerNo,
			PlayerName:  req.PlayerName,
			PlayDate:    req.PlayDate,
			CartNo:      req.CartNo,
			CompeNo:     req.CompeNo,
			CourseIndex: req.CourseIndex,
		},
		ExpirationTime: 0, // in seconds
		UpdatedAt:      time.Now(),
	}

	compeRepo.UpsertPlayerToCompe(playerCompe)
	if result && err != nil {
		slog.Error("UpsertPlayerToCompe error :", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	// trancation end
	resp.Code = http.StatusOK
	resp.Msg = "success"
	c.JSON(resp.Code, resp)
}

// ListOfficeCompe
// @Summary get office compe list
// @Description get office compe list コンペ一覧
// @Produce  json
// @Param data query request.OnlineCompeOfficeReq true "Request payload"
// @Success 200 {object} compeResp.OnlineCompeOfficeResp "response"
// @Failure 400 {object} compeResp.OnlineCompeOfficeResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.OnlineCompeOfficeResp "Unauthorized"
// @Failure 404 {object} compeResp.OnlineCompeOfficeResp "Not Found"
// @Failure 500 {object} compeResp.OnlineCompeOfficeResp "Internal Server Error"
// @Router /web/online-compe/office-compe/list [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) ListOfficeCompe(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp compeResp.OnlineCompeOfficeResp
	var req request.OnlineCompeOfficeReq

	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "params parse error",
			"module":   "ListOfficeCompe",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "params parse error",
			"module":   "ListOfficeCompe",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "GET",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	var onlineCompes []compe.OnlineCompe
	var officeCompes []compe.OfficeCompe

	playDateStr := req.PlayDate
	//TODO time zone if playDateStr == nil use createdAt nearby today
	if playDateStr == nil || *playDateStr == "" {
		dateStr := time.Now().Format("20060102")
		playDateStr = &dateStr
	}
	onlineCompes, officeCompes, err = compeRepo.ListOfficeToOnlineCompeByPlayDate(*playDateStr, authInfo.OfficeKey, *req.Offset, req.Limit)
	if err != nil {
		slog.Error("debug ListOfficeToOnlineCompeByPlayDate :", "err", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	slog.Info("onlineCompes :", "onlineCompes", onlineCompes, "officeCompes", officeCompes)

	var oldCompes []compe.OldCompe

	if len(onlineCompes) < req.Limit {
		limit := req.Limit - len(onlineCompes)
		var dateStr string
		if req.PlayDate == nil {
			dateStr = time.Now().Format("20060102")
		} else {
			dateStr = *req.PlayDate
		}

		oldCompes, err = compeRepo.ListOfficeToOldCompe(dateStr, authInfo.OfficeKey, *req.Offset, limit, false)
		if err != nil {
			slog.Error("debug ListOfficeToOldCompe :", "err", err)
			resp.Code = http.StatusInternalServerError
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}
		slog.Info("oldCompes :", "oldCompes", oldCompes)
	}

	//mergedCompes = onlineCompes + oldCompes do merge
	// resp.Data.compes = mergedCompes
	resp.Code = http.StatusOK
	resp.Msg = "success"

	mergedCompes := []compeResp.MergedCompe{}

	for _, onlineCompe := range onlineCompes {
		aggregationTypes := []string{}
		if onlineCompe.Details.CompeTypeSetting.Handy != nil {
			aggregationTypes = append(aggregationTypes, "ハンディ")
		}
		if onlineCompe.Details.CompeTypeSetting.Peoria != nil {
			//ペリア(6H) 0 、新ペリア(12H) 1、新新ペリア(9H) 2
			switch *onlineCompe.Details.CompeTypeSetting.Peoria.AggregationMethod.Type {
			case 0:
				aggregationTypes = append(aggregationTypes, "ペリア(6H)")
			case 1:
				aggregationTypes = append(aggregationTypes, "新ペリア(12H)")
			case 2:
				aggregationTypes = append(aggregationTypes, "新新ペリア(9H)")
			}
		}

		var officeCompe compe.OfficeCompe

		for _, oc := range officeCompes {
			if oc.Details.CompeNo == onlineCompe.Details.Basic.CompeNo {
				officeCompe = oc
				break
			}
		}

		slog.Info("officeCompe :", "officeCompe", officeCompe)

		compePlayers, err := playerRepo.ListCompePlayers(authInfo.OfficeKey, onlineCompe.Details.Basic.CompeNo)
		if err != nil {
			slog.Error("ListCompePlayers :", "err", err)
			resp.Code = http.StatusInternalServerError
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}

		var sharedKey string
		if officeCompe.Details.SharedKey != "" {
			sharedKey = officeCompe.Details.SharedKey
		}

		hiddenHoleSetted := false
		if len(officeCompe.Details.HiddenHole) > 0 {
			for _, hiddenHole := range officeCompe.Details.HiddenHole {
				if len(hiddenHole.HiddenHoleIndex) > 0 {
					hiddenHoleSetted = true
					break
				}
			}
		}

		mergedCompes = append(mergedCompes, compeResp.MergedCompe{
			CompeNo:   onlineCompe.Details.Basic.CompeNo,
			CompeName: onlineCompe.Details.Basic.CompeName,
			Duration: &struct {
				From time.Time `json:"from"`
				To   time.Time `json:"to"`
			}{
				From: onlineCompe.Details.Basic.Duration.From,
				To:   onlineCompe.Details.Basic.Duration.To,
			},
			CompeType:            onlineCompe.Details.CompeTypeSetting.Type,
			JoinedPlayersCount:   len(compePlayers),
			AggregationTypes:     aggregationTypes,
			PrizeConditionSetted: officeCompe.Details.PrizeConditionSetted,
			HiddenHoleSetted:     hiddenHoleSetted,
			SharedKey:            &sharedKey,
			IsFrontSystem:        false,
			ParticipationFee:     onlineCompe.Details.Basic.ParticipationFee,
		})
	}

	for _, oldCompe := range oldCompes {
		compeNo, err := strconv.Atoi(oldCompe.Details.CompeNo)
		if err != nil {
			slog.Error("oldCompe no to int :", "err", err, "oldCompe", oldCompe)
			resp.Code = http.StatusInternalServerError
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}
		playDate, err := time.Parse("2006-01-02", oldCompe.Details.PlayDate)
		if err != nil {
			slog.Error("oldCompe playDate to time :", "err", err, "oldCompe", oldCompe)
			resp.Code = http.StatusInternalServerError
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}

		aggregationTypes := []string{}

		//TODO get aggregationTypes for old compe , 0一般 1競技
		switch oldCompe.Details.CompeType {
		case "0":
			aggregationTypes = append(aggregationTypes, "ハンディ")
		case "1":
			aggregationTypes = append(aggregationTypes, "ハンディ")
		}

		var sharedKey string
		if oldCompe.Details.PublicKey != "" {
			sharedKey = oldCompe.Details.PublicKey
		}

		mergedCompes = append(mergedCompes, compeResp.MergedCompe{
			CompeNo:   compeNo,
			CompeName: oldCompe.Details.CompeName,
			Duration: &struct {
				From time.Time `json:"from"`
				To   time.Time `json:"to"`
			}{
				From: playDate, //2025-01-09
				To:   playDate.Add(time.Hour * 24),
			},
			CompeType:            nil, //no mapping for old compe
			JoinedPlayersCount:   0,
			AggregationTypes:     aggregationTypes,
			PrizeConditionSetted: false,
			HiddenHoleSetted:     false,
			SharedKey:            &sharedKey,
			IsFrontSystem:        true,
			ParticipationFee:     nil,
		})
	}

	targetMergedCompes := []compeResp.MergedCompe{}
	// filter mergedCompes by req.CompeKind req.CompeType req.FreeWord
	for _, compe := range mergedCompes {
		// Filter by CompeType if specified
		if req.CompeType != nil && compe.CompeType != nil && *compe.CompeType != *req.CompeType {
			continue
		}

		// Filter by CompeKind (aggregationType) if specified
		if req.CompeKind != nil && len(compe.AggregationTypes) > 0 {
			switch *req.CompeKind {
			case "handy":
				// Check if any aggregation type is "ハンディ"
				hasHandy := false
				for _, aggType := range compe.AggregationTypes {
					if aggType == "ハンディ" {
						hasHandy = true
						break
					}
				}
				if !hasHandy {
					continue
				}
			case "peoria":
				// Check if any aggregation type contains "ペリア"
				hasPerio := false
				for _, aggType := range compe.AggregationTypes {
					if strings.Contains(aggType, "ペリア") {
						hasPerio = true
						break
					}
				}
				if !hasPerio {
					continue
				}
			}
		}

		// Filter by FreeWord in CompeName if specified
		if req.FreeWord != nil && *req.FreeWord != "" {
			if !strings.Contains(strings.ToLower(compe.CompeName), strings.ToLower(*req.FreeWord)) {
				continue
			}
		}

		targetMergedCompes = append(targetMergedCompes, compe)
	}

	resp.Data.Compes = targetMergedCompes
	c.JSON(resp.Code, resp)
}

// ListCourse
// @Summary get course list
// @Description get course list
// @Produce  json
// @Success 200 {object} courseResp.CoursesResp "response"
// @Failure 400 {object} courseResp.CoursesResp "Bad Request ,param error"
// @Failure 401 {object} courseResp.CoursesResp "Unauthorized"
// @Failure 404 {object} courseResp.CoursesResp "Not Found"
// @Failure 500 {object} courseResp.CoursesResp "Internal Server Error"
// @Router /web/online-compe/course/list [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) ListCourse(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	var resp courseResp.CoursesResp
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "ListCourse",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Debug("debug officeId :", "officeId", authInfo.OfficeID)

	db, err := client.GetMncDBClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "compe list course get db client error",
			"module":    "ListCourse",
			"office_id": authInfo.OfficeID,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := courseRepo.CourseRepos{ConnMySQLMncdb: db}
	courses, err := handle.ListCourse(authInfo.OfficeID)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get courses error",
			"module":   "ListCourse",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data = courses

	logging.LogFormat(enum.LogDebug, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "get courses response log",
		"module":   "ListCourse",
		"request":  requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}

// ListCompePlayers
// @Summary get compe player list
// @Description get compe player list コンペ詳細・参加者
// @Produce  json
// @Success 200 {object} playerResp.JoinedPlayesResp "response"
// @Failure 400 {object} playerResp.JoinedPlayesResp "Bad Request ,param error"
// @Failure 401 {object} playerResp.JoinedPlayesResp "Unauthorized"
// @Failure 404 {object} playerResp.JoinedPlayesResp "Not Found"
// @Failure 500 {object} playerResp.JoinedPlayesResp "Internal Server Error"
// @Router /web/online-compe/compe-player/:compeNo/list [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) ListCompePlayers(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp playerResp.JoinedPlayesResp

	authInfo, err := repository.GetWebAuthInfo(c)

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "ListCompePlayers",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	compeNo, err := strconv.Atoi(c.Param("compeNo"))
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get compe no error",
			"module":   "ListCompePlayers",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	slog.Debug("debug compeNo:", "compeNo", compeNo)

	// TODO read from repo
	compePlayers, err := playerRepo.ListCompePlayers(authInfo.OfficeKey, compeNo)
	if err != nil {
		slog.Error("ListCompePlayers :", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	slog.Debug("ListCompePlayers:", "compePlayers", compePlayers)

	resp.Code = http.StatusOK
	resp.Msg = "success"

	joinedPlayers := []player.JoinedPlayer{}
	// for each item in compePlayers put into joinedPlayers
	for _, compePlayer := range compePlayers {
		joinedPlayers = append(joinedPlayers, player.JoinedPlayer{
			PlayerNo:      compePlayer.Details.PlayerNo,
			PlayerName:    compePlayer.Details.PlayerName,
			Birthday:      compePlayer.Details.Birthday,
			Gender:        compePlayer.Details.Gender,
			GlidNo:        compePlayer.Details.GlidNo,
			TeeId:         compePlayer.Details.TeeId,
			Hdcp:          compePlayer.Details.Hdcp,
			HdcpIndex:     compePlayer.Details.HdcpIndex,
			PlayingHdcp:   compePlayer.Details.PlayingHdcp,
			OfficeKey:     compePlayer.Details.OfficeKey,
			PlayDate:      compePlayer.Details.PlayDate,
			CartNo:        compePlayer.Details.CartNo,
			CourseIndex:   compePlayer.Details.CourseIndex,
			IsPaid:        compePlayer.Details.IsPaid,
			TeamClassType: compePlayer.Details.TeamClassType,
		})
	}
	resp.Data = joinedPlayers
	c.JSON(resp.Code, resp)
}

// UpdateCompePlayers
// @Summary update compe players
// @Description update compe players コンペ詳細・参加者, also remove player which is not in the request
// @Produce  json
// @Param data body request.OnlineCompeJoinedPlayersReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/compe-player/:compeNo/update [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) UpdateCompePlayers(c *gin.Context) {
	var resp compeResp.BasicResp
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "UpdateCompePlayers",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	var req request.OnlineCompeJoinedPlayersReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "params parse error",
			"module":   "UpdateCompePlayers",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	slog.Debug("debug officeId :", "officeId", authInfo.OfficeID)
	compeNoStr := c.Param("compeNo")
	compeNo, err := strconv.Atoi(compeNoStr)
	if err != nil {
		slog.Error("error :", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	slog.Debug("debug compeNo:", "compeNo", compeNo)

	onlineCompe, err := compeRepo.OnlineCompeByNo(compeNo)
	if err != nil {
		slog.Error("get online compe error :", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	var compePlayer []compe.CompePlayer
	for _, joinedPlayer := range req.JoinedPlayers {
		officeKey := authInfo.OfficeKey
		if joinedPlayer.OfficeKey != "" {
			officeKey = joinedPlayer.OfficeKey
		}

		cp := compe.CompePlayer{
			PartitionKey: "online-compe_" + compeNoStr,
			SortKey: "online-compe_player_" + officeKey +
				"_" + joinedPlayer.PlayDate +
				"_" + strconv.Itoa(joinedPlayer.CartNo) +
				"_" + strconv.Itoa(joinedPlayer.PlayerNo),
			Details: compe.CompePlayerDetails{
				CompeNo:       compeNo,
				OfficeKey:     officeKey,
				PlayerNo:      joinedPlayer.PlayerNo,
				PlayerName:    joinedPlayer.PlayerName,
				Birthday:      joinedPlayer.Birthday,
				Gender:        joinedPlayer.Gender,
				GlidNo:        joinedPlayer.GlidNo,
				TeeId:         joinedPlayer.TeeId,
				Hdcp:          joinedPlayer.Hdcp,
				HdcpIndex:     joinedPlayer.HdcpIndex,
				PlayDate:      joinedPlayer.PlayDate,
				CartNo:        joinedPlayer.CartNo,
				CourseIndex:   joinedPlayer.CourseIndex,
				TeamClassType: joinedPlayer.TeamClassType,
				IsPaid:        joinedPlayer.IsPaid,
			},
			ExpirationTime: 0, // in seconds
			UpdatedAt:      time.Now(),
		}

		handy := onlineCompe.Details.CompeTypeSetting.Handy
		if handy != nil &&
			handy.Handicap.Type != nil &&
			*handy.Handicap.Type != 1 &&
			cp.Details.HdcpIndex != nil &&
			cp.Details.Gender != nil &&
			*cp.Details.HdcpIndex != "" {
			courseRatingResult, err := playerRepo.SearchPlayerCourseRateByInteralApi(authInfo.OfficeID, cp.Details)
			if err != nil {
				slog.Warn("SearchPlayerCourseRateByInteralApi error :", "err", err, "player", cp.Details, "officeId", authInfo.OfficeID)
				courseRatingResult = &playerResp.CourseRatingResult{}
			} else {
				var playingHdcp string
				if *cp.Details.Gender == 1 {
					playingHdcp = utils.ComputeHdcp(*cp.Details.HdcpIndex, courseRatingResult.MenSlopeRating, courseRatingResult.MenCourseRating, utils.ComputePar(courseRatingResult.MenPar), handy.Handicap.HdcpAllowance)
				} else {
					playingHdcp = utils.ComputeHdcp(*cp.Details.HdcpIndex, courseRatingResult.WomenSlopeRating, courseRatingResult.WomenCourseRating, utils.ComputePar(courseRatingResult.WomenPar), handy.Handicap.HdcpAllowance)
				}

				slog.Info("compute hdcp", "playingHdcp", playingHdcp, "courseRatingResult", courseRatingResult, "compePlayer", cp)
				cp.Details.PlayingHdcp = &playingHdcp
			}
		}

		compePlayer = append(compePlayer, cp)
	}

	// Get existing players for this competition
	existingPlayers, err := playerRepo.ListCompePlayers(authInfo.OfficeKey, compeNo)
	if err != nil {
		slog.Error("ListCompePlayers for deletion check:", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	// Create a map of players in the request for quick lookup
	requestPlayerMap := make(map[string]bool)
	for _, player := range req.JoinedPlayers {
		key := fmt.Sprintf("%s_%s_%d_%d",
			player.OfficeKey,
			player.PlayDate,
			player.CartNo,
			player.PlayerNo)
		requestPlayerMap[key] = true
	}

	// Find players to delete (existing but not in request)
	for _, existingPlayer := range existingPlayers {
		parts := strings.Split(existingPlayer.SortKey, "_")
		if len(parts) < 5 {
			continue // Skip if sort key format is unexpected
		}

		key := strings.Join(parts[2:], "_") // Extract office_playdate_cartno_playerno
		if !requestPlayerMap[key] {
			// Delete this player as they're not in the request
			err := playerRepo.DeletePlayer(existingPlayer.PartitionKey, existingPlayer.SortKey)
			if err != nil {
				slog.Error("Failed to delete player:", "err", err, "player", existingPlayer.SortKey)
				// Continue with other deletions even if one fails
			}

			// Delete the corresponding PlayerCompe entry
			// Format: player_online-compe_${player_no}_${compe_no}_${cart_no}_${play_date}
			playerNo := existingPlayer.Details.PlayerNo
			cartNo := existingPlayer.Details.CartNo
			playDate := existingPlayer.Details.PlayDate

			playerCompeSortKey := fmt.Sprintf("player_online-compe_%d_%d_%d_%s",
				playerNo, compeNo, cartNo, playDate)

			//delete cache []player.PlayerCompe if key exist
			redis := client.GetAppRedisClient()
			cacheKey := fmt.Sprintf("player_online-compe_%s_%d_%s", existingPlayer.Details.OfficeKey, playerNo, playDate)
			_, err = redis.Del(context.Background(), cacheKey).Result()
			if err != nil {
				slog.Warn("Failed to delete player compes from cache", "err", err)
			}

			err = playerRepo.DeletePlayer(existingPlayer.Details.OfficeKey, playerCompeSortKey)
			if err != nil {
				slog.Error("Failed to delete player_online-compe entry:", "err", err, "key", playerCompeSortKey)
				// Continue with other operations even if this fails
			}

			if err != nil {
				slog.Error("Failed to delete player:", "err", err, "player", existingPlayer.SortKey)
				// Continue with other deletions even if one fails
			}
		}
	}

	_, err = playerRepo.UpdateJoinedPlayers(compePlayer)
	if err != nil {
		slog.Error("UpdateCompePlayers :", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "update compe players response log",
		"module":    "UpdateCompePlayers",
		"office_id": authInfo.OfficeID,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	resp.Code = http.StatusOK
	resp.Msg = "success"

	c.JSON(resp.Code, resp)
}

// Get player compes
// @Summary get player compes
// @Description get player compes 参加者 コンペ詳細
// @Produce  json
// @Param data body request.OnlineCompeJoinedPlayersReq true "Request payload"
// @Success 200 {object} playerResp.PlayerCompeResp "response"
// @Failure 400 {object} playerResp.PlayerCompeResp "Bad Request ,param error"
// @Failure 401 {object} playerResp.PlayerCompeResp "Unauthorized"
// @Failure 404 {object} playerResp.PlayerCompeResp "Not Found"
// @Failure 500 {object} playerResp.PlayerCompeResp "Internal Server Error"
// @Router /web/online-compe/player-compe/:playerNo [get]
// @Router /app/online-compe/player-compe/:playerNo [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) PlayerCompe(c *gin.Context) {
	requestId := common.GetRequestId(c)
	officeKey, err := repository.GetOfficeKey(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "PlayerCompe",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	var resp playerResp.PlayerCompeResp
	playerNo := c.Param("playerNo")
	slog.Debug("debug param playerNo:", "playerNo", playerNo)
	if playerNo == "" {
		resp.Code = http.StatusBadRequest
		resp.Msg = "playerNo is required"
		c.JSON(resp.Code, resp)
		return
	}
	playerNoInt, err := strconv.Atoi(playerNo)
	if err != nil {
		slog.Error("playerNo to int :", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	today := time.Now().Format("20060102")

	playerCompes, err := playerRepo.GetPlayerCompes(today, playerNoInt, officeKey)
	if err != nil {
		slog.Error("GetPlayerCompes :", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	respData := []playerResp.PlayerCompe{}

	// for each playerCompe get compe player
	for _, playerCompe := range playerCompes {
		compePlayer, err := playerRepo.GetCompePlayer(officeKey, playerCompe.Details.CompeNo, today, playerCompe.Details.CartNo, playerCompe.Details.PlayerNo)
		if err != nil {
			slog.Error("ListCompePlayers :", "err", err)
			continue
		}
		if compePlayer == nil {
			continue
		}

		// Create a JoinedPlayer from the compePlayer details
		joinedPlayer := player.JoinedPlayer{
			PlayerNo:      compePlayer.Details.PlayerNo,
			PlayerName:    compePlayer.Details.PlayerName,
			Birthday:      compePlayer.Details.Birthday,
			Gender:        compePlayer.Details.Gender,
			GlidNo:        compePlayer.Details.GlidNo,
			TeeId:         compePlayer.Details.TeeId,
			Hdcp:          compePlayer.Details.Hdcp,
			HdcpIndex:     compePlayer.Details.HdcpIndex,
			PlayingHdcp:   compePlayer.Details.PlayingHdcp,
			OfficeKey:     compePlayer.Details.OfficeKey,
			PlayDate:      compePlayer.Details.PlayDate,
			CartNo:        compePlayer.Details.CartNo,
			CourseIndex:   compePlayer.Details.CourseIndex,
			TeamClassType: compePlayer.Details.TeamClassType,
			IsPaid:        compePlayer.Details.IsPaid,
		}

		// Add the joined player to the map, grouped by compe number
		compeNo := playerCompe.Details.CompeNo

		onlineCompe, err := compeRepo.OnlineCompeByNo(compeNo)
		if err != nil {
			slog.Error("OnlineCompeByNo error:", "err", err, "compeNo", compeNo)
			continue
		}

		playerCompeData := playerResp.PlayerCompe{
			CompeNo:          compeNo,
			CompeName:        onlineCompe.Details.Basic.CompeName,
			CompeBasic:       onlineCompe.Details.Basic,
			CompeSetting:     onlineCompe.Details.CompeSetting,
			CompeTypeSetting: onlineCompe.Details.CompeTypeSetting,
			OtherSetting:     onlineCompe.Details.OtherSetting,
			Player:           joinedPlayer,
		}

		respData = append(respData, playerCompeData)
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data = respData

	c.JSON(resp.Code, resp)
}

// SearchPlayerInfo
// @Summary search player info
// @Description search player info コンペ詳細・参加者
// @Produce  json
// @Param data body request.SearchPlayerInfoReq true "Request payload"
// @Success 200 {object} playerResp.PlayerInfoResp "response"
// @Failure 400 {object} playerResp.PlayerInfoResp "Bad Request ,param error"
// @Failure 401 {object} playerResp.PlayerInfoResp "Unauthorized"
// @Failure 404 {object} playerResp.PlayerInfoResp "Not Found"
// @Failure 500 {object} playerResp.PlayerInfoResp "Internal Server Error"
// @Router /web/online-compe/player-info/search [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) SearchPlayerInfo(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "search player info get office id error",
			"module":   "SearchPlayerInfo",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Debug("debug officeId :", "officeId", authInfo.OfficeID)

	var req request.SearchPlayerInfoReq
	var resp playerResp.PlayerInfoResp
	if err := c.ShouldBindQuery(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "search player info params parse error",
			"module":   "SearchPlayerInfo",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	searchType := req.SearchType
	switch searchType {
	case 1:
		name := strings.ReplaceAll(req.PlayerName, " ", "")
		birthday := req.Birthday
		if name == "" || birthday == "" {
			logging.LogFormat(enum.LogError, map[string]any{
				"category": enum.LogCategoryApp,
				"message":  "search player info name or birthday empty",
				"module":   "SearchPlayerInfo",
				"request":  requestId,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Code = http.StatusBadRequest
			resp.Msg = errors.New("name or birthday empty").Error()
			c.JSON(resp.Code, resp)
			return
		}

		searchPlayers, err := playerRepo.SearchPlayerInfoByInternalApi(name, birthday)
		if err != nil {
			logging.LogFormat(enum.LogError, map[string]any{
				"category": enum.LogCategoryApp,
				"message":  "search player info by internal api error",
				"module":   "SearchPlayerInfo",
				"request":  requestId,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Code = http.StatusBadRequest
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}

		resp.Code = http.StatusOK
		resp.Msg = "success"
		resp.Data = searchPlayers
		c.JSON(resp.Code, resp)
	case 2:
		if req.GlidNo == "" || req.HdcpDate == "" {
			logging.LogFormat(enum.LogError, map[string]any{
				"category": enum.LogCategoryApp,
				"message":  "search player info glid_no or play_date empty",
				"module":   "SearchPlayerInfo",
				"request":  requestId,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Code = http.StatusBadRequest
			resp.Msg = errors.New("glid_no or hdcp_date empty").Error()
			c.JSON(resp.Code, resp)
			return
		}
		// get time from req.HdcpDate and format as yyyy/MM/dd for targetHdcpDate
		parsedDate, err := time.Parse("2006-01-02", req.HdcpDate)
		if err != nil {
			slog.Error("parse date error", "err", err)
			resp.Code = http.StatusBadRequest
			resp.Msg = errors.New("format hdcp_date error").Error()
			c.JSON(resp.Code, resp)
			return

		}
		targetHdcpDate := parsedDate.Format("2006/01/02")

		searchPlayer, err := playerRepo.SearchPlayerInfoByExternalApi(req.GlidNo, targetHdcpDate)
		if err != nil {
			logging.LogFormat(enum.LogError, map[string]any{
				"category": enum.LogCategoryApp,
				"message":  "search player info by external api error",
				"module":   "SearchPlayerInfo",
				"request":  requestId,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Code = http.StatusBadRequest
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}

		resp.Code = http.StatusOK
		resp.Msg = "success"
		resp.Data = []player.PlayerInfo{searchPlayer}
		c.JSON(resp.Code, resp)
	case 3:
		name := strings.ReplaceAll(req.PlayerName, " ", "")
		birthday := req.Birthday
		if name == "" || birthday == "" || req.HdcpDate == "" {
			logging.LogFormat(enum.LogError, map[string]any{
				"category": enum.LogCategoryApp,
				"message":  "search player info name or birthday or play_date empty",
				"module":   "SearchPlayerInfo",
				"request":  requestId,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Code = http.StatusBadRequest
			resp.Msg = errors.New("name or birthday or play_date empty").Error()
			c.JSON(resp.Code, resp)
			return
		}
		searchPlayers, err := playerRepo.SearchPlayerInfoByInternalApi(name, birthday)
		if err != nil {
			logging.LogFormat(enum.LogError, map[string]any{
				"category": enum.LogCategoryApp,
				"message":  "search player info by internal api error",
				"module":   "SearchPlayerInfo",
				"request":  requestId,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
						"path":   c.FullPath(),
					},
				},
			})

			resp.Code = http.StatusBadRequest
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}
		data := make([]player.PlayerInfo, 0)
		// get time from req.HdcpDate and format as yyyy/MM/dd for targetHdcpDate
		parsedDate, err := time.Parse("2006-01-02", req.HdcpDate)
		if err != nil {
			slog.Error("parse date error", "err", err)
			resp.Code = http.StatusBadRequest
			resp.Msg = errors.New("format hdcp_date error").Error()
			c.JSON(resp.Code, resp)
			return

		}
		targetHdcpDate := parsedDate.Format("2006/01/02")
		for _, searchPlayer := range searchPlayers {
			sp, err := playerRepo.SearchPlayerInfoByExternalApi(searchPlayer.GlidNo, targetHdcpDate)
			if err != nil {
				continue
			}
			data = append(data, sp)
		}

		resp.Code = http.StatusOK
		resp.Msg = "success"
		resp.Data = data
		c.JSON(resp.Code, resp)
	default:
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "search player info type error",
			"module":   "SearchPlayerInfo",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = errors.New("error search type").Error()
		c.JSON(resp.Code, resp)
		return
	}
}

// TeeSheet
// @Summary get TeeSheet
// @Description get TeeSheet
// @Produce  json
// @Param date_str query string false "data str like 20200101"
// @Success 200 {object} teeResp.TeeSheetResp "response"
// @Failure 400 {object} teeResp.TeeSheetResp "Bad Request ,param error"
// @Failure 401 {object} teeResp.TeeSheetResp "Unauthorized"
// @Failure 404 {object} teeResp.TeeSheetResp "Not Found"
// @Failure 500 {object} teeResp.TeeSheetResp "Internal Server Error"
// @Router /web/online-compe/tee/sheet [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) TeeSheet(c *gin.Context) {

	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	var resp teeResp.TeeSheetResp
	//get dateStr from query
	dateStr := c.Query("date_str")
	if dateStr == "" {
		dateStr = time.Now().Format("20060102")
	}

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "TeeSheet",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	teeSheets, err := playerRepo.TeeSheetsByDateStr(dateStr, authInfo.OfficeKey)
	slog.Debug("debug TeeSheetsByDateStr:", "teeSheets", teeSheets)
	if err != nil {
		// handle error
		return
	}

	// list courses no hole
	db, err := client.GetMncDBClient()
	if err != nil {
		slog.Error("get db client error", "err", err)
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := courseRepo.CourseRepos{ConnMySQLMncdb: db}
	coursesNoHole, err := handle.ListCoursesWithoutHole(authInfo.OfficeID)

	if err != nil {
		slog.Error("get courses error", "err", err)
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// list old compes TODO 100
	oldCompes, err := compeRepo.ListOfficeToOldCompe(dateStr, authInfo.OfficeKey, 0, 100, false)
	if err != nil {
		slog.Error("debug ListOfficeToOldCompe :", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	// Create a slice to store the results
	teeCourseData := []teeResp.TeeCourseData{}

	// Use a mutex to safely append to teeCourseData from multiple goroutines
	var mutex sync.Mutex

	// Create a WaitGroup to wait for all goroutines to complete
	var wg sync.WaitGroup

	// Process each teeSheet in parallel
	for _, teeSheet := range teeSheets {
		// Add a counter to the WaitGroup
		wg.Add(1)

		// Create a goroutine for each teeSheet
		go func(teeSheet tee.TeeSheet) {
			// Ensure the WaitGroup counter is decremented when the goroutine completes
			defer wg.Done()

			cartNo, err := strconv.Atoi(teeSheet.Details.CartNo)
			if err != nil {
				slog.Error("cartNo to int error", "err", err)
				return
			}

			teePlayerData := []teeResp.TeePlayerData{}
			for _, player := range teeSheet.Details.TeePlayer {

				var lockerNo int
				if player.LockerNo != "" {
					lockerNo, err = strconv.Atoi(player.LockerNo)
					if err != nil {
						slog.Error("lockerNo to int error", "err", err)
						continue
					}
				}

				playerCompes, err := playerRepo.GetPlayerCompes(dateStr, player.PlayerNo, authInfo.OfficeKey)
				if err != nil {
					slog.Error("debug GetPlayerCompes :", "err", err)
					continue
				}

				joinedCompes := make([]struct {
					CompeNo int `json:"compe_no"`
				}, 0)

				for _, pc := range playerCompes {
					joinedCompes = append(joinedCompes, struct {
						CompeNo int `json:"compe_no"`
					}{
						CompeNo: pc.Details.CompeNo,
					})
				}

				teePlayerData = append(teePlayerData, teeResp.TeePlayerData{
					PlayerNo:     player.PlayerNo,
					PlayerName:   player.PlayerName,
					Gender:       &player.Gender,
					Birthday:     &player.Birthday,
					GlidNo:       player.GlidNo,
					TeeId:        &player.TeeId,
					HdcpIndex:    &player.HdcpIndex,
					Hdcp:         &player.Hdcp,
					LockerNo:     &lockerNo,
					JoinedCompes: joinedCompes,
				})
			}

			slog.Debug("debug teePlayerData:", "teePlayerData", teePlayerData)

			var firstCompeNo int
			var delegateCompe *teeResp.DelegateCompe

			//get one CompeNo from all teePlayerData
			for _, player := range teePlayerData {
				if len(player.JoinedCompes) > 0 {
					firstCompeNo = player.JoinedCompes[0].CompeNo
					break
				}
			}
			if firstCompeNo > 0 {
				firstCompe, err := compeRepo.OnlineCompeByNo(firstCompeNo)
				if err != nil {
					slog.Error("Error OnlineCompeByNo :", "err", err)
					return
				}
				// firstCompe to delegateCompe
				delegateCompe = &teeResp.DelegateCompe{
					CompeName:     firstCompe.Details.Basic.CompeName,
					CompeNo:       firstCompe.Details.Basic.CompeNo,
					IsFrontSystem: false,
				}
			} else {
				// get minimum OldCompeNo from teeSheet.Details.TeePlayer
				var minOldCompeNo int = 0
				for _, player := range teeSheet.Details.TeePlayer {
					if player.OldCompeNo == "" {
						continue
					}
					oldCompeNo, err := strconv.Atoi(player.OldCompeNo)
					if err != nil {
						slog.Error("oldCompeNo to int :", "err", err)
						continue
					}
					if oldCompeNo > 0 && (minOldCompeNo == 0 || oldCompeNo < minOldCompeNo) {
						minOldCompeNo = oldCompeNo
					}
				}

				if minOldCompeNo > 0 {
					var oldCompe *compe.OldCompe
					for _, oc := range oldCompes {
						if oc.Details.CompeNo == strconv.Itoa(minOldCompeNo) {
							oldCompe = &oc
							break
						}
					}
					if oldCompe == nil {
						slog.Warn("oldCompe not found :", "minOldCompeNo", minOldCompeNo)
						return
					}
					delegateCompe = &teeResp.DelegateCompe{
						CompeName:     oldCompe.Details.CompeName,
						CompeNo:       minOldCompeNo,
						IsFrontSystem: true,
					}
				}
			}

			slog.Debug("debug delegateCompe:", "delegateCompe", delegateCompe)

			// add cart data
			cartData := []teeResp.TeeCartData{}
			cartData = append(cartData, teeResp.TeeCartData{
				CartNo:             cartNo,
				StartTime:          teeSheet.Details.StartTime,
				ScheduledStartTime: teeSheet.Details.ScheduledStartTime,
				DelegateCompe:      delegateCompe,
				Players:            teePlayerData,
			})
			slog.Debug("debug cartData:", "cartData", cartData)

			courseName := "未確定"
			courseIndex := ""
			for _, course := range coursesNoHole {
				if course.CourseIndex == teeSheet.Details.StartCourseIndex ||
					(teeSheet.Details.StartCourseIndex == "" && course.CourseIndex == teeSheet.Details.ScheduledStartCourseIndex) {
					courseName = course.CourseName
					courseIndex = course.CourseIndex
					break
				}
			}

			// Create the TeeCourseData
			teeCourseItem := teeResp.TeeCourseData{
				CourseIndex: courseIndex,
				CourseName:  courseName,
				CartData:    cartData,
			}

			slog.Debug("debug teeCourseItem:", "teeCourseItem", teeCourseItem)

			// Lock the mutex before appending to the shared slice
			mutex.Lock()
			teeCourseData = append(teeCourseData, teeCourseItem)
			mutex.Unlock()
		}(teeSheet)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	//sort teeCourseData by cart no asc
	sort.Slice(teeCourseData, func(i, j int) bool {
		return teeCourseData[i].CartData[0].CartNo < teeCourseData[j].CartData[0].CartNo
	})

	resp.Data = teeCourseData
	resp.Msg = "success"
	resp.Code = http.StatusOK

	c.JSON(resp.Code, resp)

}

// TeeSheet
// @Summary update TeeSheet by date and cart no
// @Description update TeeSheet  by date and cart no
// @Produce  json
// @Param data body teeReq.TeeSheetPlayerUpdateReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/tee/sheet/player/update [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) TeeSheetPlayerUpdate(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "TeeInfo",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Debug("debug authInfo:", "officeKey", authInfo.OfficeKey)

	var req teeReq.TeeSheetPlayerUpdateReq
	var resp compeResp.BasicResp
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "bind body error",
			"module":   "TeeSheetPlayerUpdate",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	//get teeSheet by TeeSheetByDateStrAndCartNo

	scheduledStartTime := "0000"
	if req.ScheduledStartTime != nil && *req.ScheduledStartTime != "" {
		scheduledStartTime = strings.ReplaceAll(*req.ScheduledStartTime, ":", "")
	}
	teeSheet, err := playerRepo.TeeSheetByDateStrAndCartNo(req.PlayDate, authInfo.OfficeKey, req.CartNo, scheduledStartTime)
	if err != nil {
		slog.Error("TeeSheetByDateStrAndCartNo error", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	if teeSheet == nil {
		resp.Code = http.StatusNotFound
		resp.Msg = "TeeSheet not found"
		c.JSON(resp.Code, resp)
		return
	}

	// Find and update the player in teeSheet.Details.TeePlayer
	playerFound := false
	for i, player := range teeSheet.Details.TeePlayer {
		if player.PlayerNo == req.PlayerNo {
			// Update player fields if provided
			if req.GlidNo != nil {
				teeSheet.Details.TeePlayer[i].GlidNo = *req.GlidNo
			}
			if req.HdcpIndex != nil {
				teeSheet.Details.TeePlayer[i].HdcpIndex = *req.HdcpIndex
			}
			if req.Hdcp != nil {
				teeSheet.Details.TeePlayer[i].Hdcp = *req.Hdcp
			}
			if req.Birthday != nil {
				teeSheet.Details.TeePlayer[i].Birthday = *req.Birthday
			}
			if req.Gender != nil {
				teeSheet.Details.TeePlayer[i].Gender = *req.Gender
			}
			playerFound = true
			break
		}
	}

	if !playerFound {
		resp.Code = http.StatusNotFound
		resp.Msg = "Player not found in tee sheet"
		c.JSON(resp.Code, resp)
		return
	}

	// update teeSheet by UpdateTeeSheetByCartNoAndPlaydate
	err = playerRepo.UpdateTeeSheetByCartNoAndPlaydate(authInfo.OfficeKey, req.PlayDate, req.CartNo, scheduledStartTime, *teeSheet)
	if err != nil {
		slog.Error("UpdateTeeSheetByCartNoAndPlaydate error", "err", err)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	c.JSON(resp.Code, resp)

}

// @Summary get TeeInfo
// @Description get TeeInfo コンペ詳細・参加者
// @Produce  json
// @Success 200 {object} teeResp.TeeInfoResp "response"
// @Failure 400 {object} teeResp.TeeInfoResp "Bad Request ,param error"
// @Failure 401 {object} teeResp.TeeInfoResp "Unauthorized"
// @Failure 404 {object} teeResp.TeeInfoResp "Not Found"
// @Failure 500 {object} teeResp.TeeInfoResp "Internal Server Error"
// @Router /web/online-compe/tee/info [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) TeeInfo(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "TeeInfo",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	slog.Debug("debug authInfo:", "officeKey", authInfo.OfficeKey)

	var resp teeResp.TeeInfoResp

	resp.Code = http.StatusOK
	resp.Msg = "success"
	officeTeeInfo, err := teeRepo.TeeInfo(authInfo.OfficeKey)
	if err != nil {
		slog.Error("error TeeInfo :", "err", err)
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	if officeTeeInfo == nil {
		slog.Warn("no TeeInfo :", "officeKey", authInfo.OfficeKey)
		resp.Code = http.StatusNotFound
		resp.Msg = "no TeeInfo"
		c.JSON(resp.Code, resp)
		return
	}
	resp.Data = officeTeeInfo.Details.TeeInfo
	c.JSON(resp.Code, resp)
}

// TODO
// @Summary get player from tee
// @Description get TeeInfo コンペ詳細・参加者
// @Param data query teeReq.TeeSheetPlayerSearchReq true "Request payload"
// @Produce  json
// @Success 200 {object} teeResp.TeePlayerResp "response"
// @Failure 400 {object} teeResp.TeePlayerResp "Bad Request ,param error"
// @Failure 401 {object} teeResp.TeePlayerResp "Unauthorized"
// @Failure 404 {object} teeResp.TeePlayerResp "Not Found"
// @Failure 500 {object} teeResp.TeePlayerResp "Internal Server Error"
// @Router /web/online-compe/tee/sheet/player/search [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) TeeSheetPlayerSearch(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get auth info error",
			"module":   "TeeSheetPlayerSearch",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	slog.Debug("debug authInfo:", "office_key", authInfo.OfficeKey)

	var req teeReq.TeeSheetPlayerSearchReq
	var resp teeResp.TeePlayerResp

	if err := c.ShouldBindQuery(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "params parse error",
			"module":   "ListOfficeCompe",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	slog.Info("TeeSheetPlayerSearch request", "req", req)

	playDateStr := time.Now().Format("20060102")
	if req.PlayDate != nil && *req.PlayDate != "" {
		playDateStr = *req.PlayDate
	}

	teeSheets, err := playerRepo.TeeSheetsByDateStr(playDateStr, authInfo.OfficeKey)
	if err != nil {
		slog.Error("debug TeeSheetsByDateStr", "err", err, "playDateStr", playDateStr, "officeKey", authInfo.OfficeKey)
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	slog.Info("TeeSheetsByDateStr :", "teeSheets", teeSheets)

	teePlayers := []tee.TeePlayer{}
	// for each teeSheet in teeSheets search  TeeSheetDetail.TeePlayer by player_name and birthday
	for _, teeSheet := range teeSheets {
		for _, player := range teeSheet.Details.TeePlayer {
			if player.PlayerName == req.PlayerName && player.Birthday == req.Birthday {
				teePlayers = append(teePlayers, player)
			}
		}
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data = teePlayers

	c.JSON(resp.Code, resp)

}

// LeaderboardRanking
// @Summary get leaderboard ranking
// @Description get leaderboard ranking コンペ一覧・リーダボード
// @Param aggregation_type query string true "data str handy or peoria"
// @Success 200 {object} compeResp.LeaderBoardRankingResp "response"
// @Failure 400 {object} compeResp.LeaderBoardRankingResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.LeaderBoardRankingResp "Unauthorized"
// @Failure 404 {object} compeResp.LeaderBoardRankingResp "Not Found"
// @Failure 500 {object} compeResp.LeaderBoardRankingResp "Internal Server Error"
// @Router /web/online-compe/leaderboard/ranking/:compeNo [get]
// @Router /app/online-compe/leaderboard/ranking/:compeNo [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) LeaderboardRanking(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	var resp compeResp.LeaderBoardRankingResp
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "LeaderboardRanking",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	// Get compeNo from request parameter
	compeNo := c.Param("compeNo")
	aggregationType := c.Query("aggregation_type")

	slog.Info("LeaderboardRanking :", "compeNo", compeNo, "aggregationType", aggregationType, "officeId", authInfo.OfficeID, "officeKey", authInfo.OfficeKey, "requestId", requestId)

	dateStr, err := compeRepo.LeaderboardRankingLastestDateByCompeNo(compeNo, authInfo.OfficeKey)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	latestRanking, err := compeRepo.LeaderboardRankingByCompeNo(compeNo, aggregationType, authInfo.OfficeKey, dateStr)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "Failed to get leaderboard ranking",
			"module":   "LeaderboardRanking",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	if latestRanking == nil {
		resp.Code = http.StatusNotFound
		resp.Msg = "no data"
		c.JSON(resp.Code, resp)
		return
	}

	resp.Code = http.StatusOK
	resp.Data = struct {
		compe.RankingDetails
		UpdatedAt string `json:"updated_at"`
	}{
		RankingDetails: latestRanking.Details,
		UpdatedAt:      latestRanking.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	c.JSON(resp.Code, resp)
}

// LeaderboardRankingShared
// @Summary get leaderboard ranking by share key
// @Description get leaderboard ranking コンペ一覧・リーダボード
// @Param aggregation_type query string true "data str handy or peoria"
// @Param compe_no query string true "compe no"
// @Success 200 {object} compeResp.LeaderBoardRankingResp "response"
// @Failure 400 {object} compeResp.LeaderBoardRankingResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.LeaderBoardRankingResp "Unauthorized"
// @Failure 404 {object} compeResp.LeaderBoardRankingResp "Not Found"
// @Failure 500 {object} compeResp.LeaderBoardRankingResp "Internal Server Error"
// @Router /web/online-compe/leaderboard/ranking/shared/:compeNo [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) LeaderboardRankingShared(c *gin.Context) {
	requestId := common.GetRequestId(c)
	shareKey := c.Param("shareKey")
	aggregationType := c.Query("aggregation_type")
	compeNo := c.Query("compe_no")
	slog.Info("LeaderboardRankingShared :", "shareKey", shareKey, "requestId", requestId, "aggregationType", aggregationType)

	// get compeNo, officeId, officeKey from redis
	redis := client.GetRedisClient()
	result, err := redis.Get(context.Background(), compeNo+":shareKey:"+shareKey).Result()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking get value from share key error",
			"module":   "LeaderboardRankingShared",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	if len(strings.Split(result, "_")) != 3 {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking get value from share key error",
			"module":   "LeaderboardRankingShared",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, "share key error")
		return
	}

	officeKey := strings.Split(result, "_")[2]

	var resp compeResp.LeaderBoardRankingResp

	dateStr, err := compeRepo.LeaderboardRankingLastestDateByCompeNo(compeNo, officeKey)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	latestRanking, err := compeRepo.LeaderboardRankingByCompeNo(compeNo, aggregationType, officeKey, dateStr)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "Failed to get leaderboard ranking",
			"module":   "LeaderboardRankingShared",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	if latestRanking == nil {
		resp.Code = http.StatusNotFound
		resp.Msg = "no data"
		c.JSON(resp.Code, resp)
		return
	}
	resp.Code = http.StatusOK
	resp.Data = struct {
		compe.RankingDetails
		UpdatedAt string `json:"updated_at"`
	}{
		RankingDetails: latestRanking.Details,
		UpdatedAt:      latestRanking.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	c.JSON(resp.Code, resp)
}

// LeaderboardRankingType
// @Summary get leaderboard ranking type
// @Description get leaderboard ranking コンペ一覧・リーダボード Type
// @Success 200 {object} compeResp.LeaderBoardRankingTypeResp "response"
// @Failure 400 {object} compeResp.LeaderBoardRankingTypeResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.LeaderBoardRankingTypeResp "Unauthorized"
// @Failure 404 {object} compeResp.LeaderBoardRankingTypeResp "Not Found"
// @Failure 500 {object} compeResp.LeaderBoardRankingTypeResp "Internal Server Error"
// @Router /web/online-compe/leaderboard/ranking/type/:compeNo [get]
// @Router /app/online-compe/leaderboard/ranking/type/:compeNo [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) LeaderboardRankingType(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	var resp compeResp.LeaderBoardRankingTypeResp
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "LeaderboardRanking",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	// Get compeNo from request parameter
	compeNo := c.Param("compeNo")

	dateStr, err := compeRepo.LeaderboardRankingLastestDateByCompeNo(compeNo, authInfo.OfficeKey)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	types, err := compeRepo.LeaderboardRankingTypeByCompeNoAndDate(compeNo, authInfo.OfficeKey, dateStr)
	if err != nil {
		slog.Error("operation leaderboard ranking type by compe id", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	resp.Code = http.StatusOK
	resp.Data.RankingType = types
	c.JSON(resp.Code, resp)
}

// LeaderboardRankingTypeShared
// @Summary get leaderboard ranking type by sharekey
// @Description get leaderboard ranking コンペ一覧・リーダボード Type
// @Param compe_no query string true "compe no"
// @Success 200 {object} compeResp.LeaderBoardRankingTypeResp "response"
// @Failure 400 {object} compeResp.LeaderBoardRankingTypeResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.LeaderBoardRankingTypeResp "Unauthorized"
// @Failure 404 {object} compeResp.LeaderBoardRankingTypeResp "Not Found"
// @Failure 500 {object} compeResp.LeaderBoardRankingTypeResp "Internal Server Error"
// @Router /web/online-compe/leaderboard/ranking/type/shared/:compeNo [get]
// @Tags OnlineCompe
func (rec *OnlineCompe) LeaderboardRankingTypeShared(c *gin.Context) {
	requestId := common.GetRequestId(c)
	shareKey := c.Param("shareKey")
	compeNo := c.Query("compe_no")

	// get compeNo, officeId, officeKey from redis
	redis := client.GetRedisClient()
	result, err := redis.Get(context.Background(), compeNo+":shareKey:"+shareKey).Result()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking get value from share key error",
			"module":   "LeaderboardRankingTypeShared",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	if len(strings.Split(result, "_")) != 3 {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking get value from share key error",
			"module":   "LeaderboardRankingTypeShared",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, "share key error")
		return
	}

	officeKey := strings.Split(result, "_")[2]

	var resp compeResp.LeaderBoardRankingTypeResp

	dateStr, err := compeRepo.LeaderboardRankingLastestDateByCompeNo(compeNo, officeKey)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	types, err := compeRepo.LeaderboardRankingTypeByCompeNoAndDate(compeNo, officeKey, dateStr)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "Failed to get leaderboard ranking type",
			"module":   "LeaderboardRankingTypeShared",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	resp.Code = http.StatusOK
	resp.Data.RankingType = types
	c.JSON(resp.Code, resp)
}

// LeaderboardRankingShareKeyCreate
// @Summary create leaderboard ranking share key
// @Description create leaderboard ranking share key
// @Produce  json
// @Param data body request.LeaderboardRankingShareKeyCreateReq true "Request payload"
// @Success 200 {object} compeResp.LeaderboardRankingShareKeyCreateResp "response"
// @Failure 400 {object} compeResp.LeaderboardRankingShareKeyCreateResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.LeaderboardRankingShareKeyCreateResp "Unauthorized"
// @Failure 404 {object} compeResp.LeaderboardRankingShareKeyCreateResp "Not Found"
// @Failure 500 {object} compeResp.LeaderboardRankingShareKeyCreateResp "Internal Server Error"
// @Router /web/online-compe/leaderboard/ranking/share-key/create [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) LeaderboardRankingShareKeyCreate(c *gin.Context) {
	requestId := common.GetRequestId(c)
	authInfo, err := repository.GetWebAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "LeaderboardRankingShareKeyCreate",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}
	var resp compeResp.LeaderboardRankingShareKeyCreateResp
	var req request.LeaderboardRankingShareKeyCreateReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "create share key params parse error",
			"module":   "LeaderboardRankingShareKeyCreate",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	onlineCompe, err := compeRepo.OnlineCompeByNo(req.CompeNo)
	if err != nil {
		slog.Error("get online compe error :", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	expiry := time.Duration(time.Until(onlineCompe.Details.Basic.Duration.To).Hours()) * time.Hour
	// plus extra 90 days
	expiry += time.Hour * 24 * 90

	shareKey, err := compeRepo.LeaderboardRankingShareKeyCreate(req, authInfo.OfficeID, authInfo.OfficeKey, expiry)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "create share key error",
			"module":   "LeaderboardRankingShareKeyCreate",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	resp.Data = struct {
		ShareKey string `json:"share_key"`
	}{
		ShareKey: shareKey,
	}
	c.JSON(resp.Code, resp)
}

// LeaderboardRankingShare
// @Summary get leaderboard ranking
// @Description get leaderboard ranking コンペ一覧・リーダボード
// @Produce  json
// @Param data body request.LeaderboardRankingShareReq true "Request payload"
// @Success 200 {object} compeResp.BasicResp "response"
// @Failure 400 {object} compeResp.BasicResp "Bad Request ,param error"
// @Failure 401 {object} compeResp.BasicResp "Unauthorized"
// @Failure 404 {object} compeResp.BasicResp "Not Found"
// @Failure 500 {object} compeResp.BasicResp "Internal Server Error"
// @Router /web/online-compe/leaderboard/ranking/share [post]
// @Tags OnlineCompe
func (rec *OnlineCompe) LeaderboardRankingShare(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp compeResp.BasicResp
	var req request.LeaderboardRankingShareReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking params parse error",
			"module":   "LeaderboardRankingShare",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	redis := client.GetRedisClient()
	result, err := redis.Get(context.Background(), strconv.Itoa(req.CompeNo)+":shareKey:"+req.ShareKey).Result()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking get value from share key error",
			"module":   "LeaderboardRankingShare",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	if len(strings.Split(result, "_")) != 3 {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking get value from share key error",
			"module":   "LeaderboardRankingShare",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = errors.New("share key error").Error()
		c.JSON(resp.Code, resp)
		return
	}

	sharedUrl := configs.GetShareConfig().ShareUrl

	onlineCompe, err := compeRepo.OnlineCompeByNo(req.CompeNo)

	if err != nil {
		slog.Error("operation get compe by no", "err", err)
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	err = common.SendTemplatedEmail(req.Email, sharedUrl, req.ShareKey, req.CompeNo,
		onlineCompe.Details.Basic.CompeName,
		onlineCompe.Details.Basic.Duration.From,
		onlineCompe.Details.Basic.Duration.To)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "leaderboard ranking send email error",
			"module":   "LeaderboardRankingShare",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "POST",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "success"
	c.JSON(resp.Code, resp)
}

// For phase2
func (rec *OnlineCompe) Pairing(c *gin.Context) {
	// get compe id from request
	requestId := common.GetRequestId(c)
	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "get office id error",
			"module":   "ListOfficeCompe",
			"request":  requestId,
			"err":      err,
		})
		c.JSON(http.StatusBadRequest, err.Error())
		return
	}

	var resp compeResp.PairingResp

	slog.Debug("debug GetOfficeId:", "officeId", officeId)

	resp.Code = http.StatusOK

	//Build PairingResp
	// ranking from db
	// course from db
	// old players (sort key player_20250303_120)  from db
	// logic get pairing based php code app/Http/Controllers/User/CompetitionHistoryController.php 621-666

	c.JSON(resp.Code, resp)

}
