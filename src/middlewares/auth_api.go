package middlewares

import (
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"mi-restful-api/utils"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func AuthApi() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>er("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "No Authorization header provided"})
			c.Abort()
			return
		}

		const bearerPrefix = "Bearer "
		if authHeader == enum.WebMockAuthToken {
			c.Set("userId", "mock_user_id")
			c.Set("office_id", configs.GetMockConfig().OfficeId)
			c.Set("office_key", configs.GetMockConfig().OfficeKey)
			c.Set("client_type", enum.ClientTypeWeb)
		} else if len(authHeader) > len(bearerPrefix) && authHeader[:len(bearerPrefix)] == bearerPrefix {
			tokenString := authHeader[len(bearerPrefix):]

			token, err := utils.ParseToken(tokenString)
			if err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
				c.Abort()
				return
			}

			if err = utils.AccessTokenExist(tokenString); err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Deleted token"})
				c.Abort()
				return
			}

			if claims, ok := token.Claims.(jwt.MapClaims); ok {
				userId := claims["userId"].(string)
				officeId := claims["officeId"].(string)
				officeKey, ok := claims["officeKey"].(string)
				if !ok {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or missing office key in token"})
					c.Abort()
					return
				}
				exp := claims["exp"].(float64)
				if int64(exp) < time.Now().Unix() {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Expired token"})
					c.Abort()
					return
				}
				c.Set("userId", userId)
				c.Set("office_id", officeId)
				c.Set("office_key", officeKey)
				c.Set("client_type", enum.ClientTypeWeb)
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
				c.Abort()
				return
			}

		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid Authorization header format"})
			c.Abort()
			return
		}

		c.Next()
	}
}
