package common

import (
	"mi-restful-api/model/course"
)

// FindCourseNameByIndex finds the course name by matching the given course index with courses.
// Returns the course name if found, otherwise returns the fallback name "未定".
func FindCourseNameByIndex(courses []course.Course, courseIndex string) string {
	// Find course name by matching courseIndex with course.CourseIndex
	for _, course := range courses {
		if course.CourseIndex == courseIndex {
			return course.CourseName
		}
	}
	// Fallback to default if no match found
	return "未定"
}

// CreateCourseIndexMap creates a map for quick course name lookup by course index.
// This is useful when you need to perform multiple lookups.
func CreateCourseIndexMap(courses []course.Course) map[string]string {
	courseMap := make(map[string]string)
	for _, course := range courses {
		courseMap[course.CourseIndex] = course.CourseName
	}
	return courseMap
}

// FindCourseNameByIndexWithMap finds the course name using a pre-built course index map.
// Returns the course name if found, otherwise returns the fallback name "未定".
func FindCourseNameByIndexWithMap(courseMap map[string]string, courseIndex string) string {
	if courseName, exists := courseMap[courseIndex]; exists {
		return courseName
	}
	return "未定"
}
