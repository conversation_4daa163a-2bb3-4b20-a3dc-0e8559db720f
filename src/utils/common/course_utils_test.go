package common

import (
	"mi-restful-api/model/course"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFindCourseNameByIndex(t *testing.T) {
	// Setup test data
	courses := []course.Course{
		{CourseIndex: "0", CourseName: "East Course"},
		{CourseIndex: "1", CourseName: "West Course"},
		{CourseIndex: "2", CourseName: "North Course"},
	}

	tests := []struct {
		name        string
		courseIndex string
		expected    string
	}{
		{
			name:        "Found existing course",
			courseIndex: "1",
			expected:    "West Course",
		},
		{
			name:        "Found first course",
			courseIndex: "0",
			expected:    "East Course",
		},
		{
			name:        "Course not found",
			courseIndex: "99",
			expected:    "未定",
		},
		{
			name:        "Empty course index",
			courseIndex: "",
			expected:    "未定",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FindCourseNameByIndex(courses, tt.courseIndex)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFindCourseNameByIndexEmptyCourses(t *testing.T) {
	// Test with empty courses slice
	courses := []course.Course{}
	result := FindCourseNameByIndex(courses, "0")
	assert.Equal(t, "未定", result)
}

func TestCreateCourseIndexMap(t *testing.T) {
	courses := []course.Course{
		{CourseIndex: "0", CourseName: "East Course"},
		{CourseIndex: "1", CourseName: "West Course"},
		{CourseIndex: "2", CourseName: "North Course"},
	}

	courseMap := CreateCourseIndexMap(courses)

	expected := map[string]string{
		"0": "East Course",
		"1": "West Course",
		"2": "North Course",
	}

	assert.Equal(t, expected, courseMap)
}

func TestCreateCourseIndexMapEmpty(t *testing.T) {
	courses := []course.Course{}
	courseMap := CreateCourseIndexMap(courses)
	assert.Empty(t, courseMap)
}

func TestFindCourseNameByIndexWithMap(t *testing.T) {
	courseMap := map[string]string{
		"0": "East Course",
		"1": "West Course",
		"2": "North Course",
	}

	tests := []struct {
		name        string
		courseIndex string
		expected    string
	}{
		{
			name:        "Found existing course",
			courseIndex: "1",
			expected:    "West Course",
		},
		{
			name:        "Course not found",
			courseIndex: "99",
			expected:    "未定",
		},
		{
			name:        "Empty course index",
			courseIndex: "",
			expected:    "未定",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FindCourseNameByIndexWithMap(courseMap, tt.courseIndex)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFindCourseNameByIndexWithMapEmpty(t *testing.T) {
	courseMap := map[string]string{}
	result := FindCourseNameByIndexWithMap(courseMap, "0")
	assert.Equal(t, "未定", result)
}
