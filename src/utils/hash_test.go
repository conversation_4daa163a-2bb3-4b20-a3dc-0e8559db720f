package utils_test

import (
	"bytes"
	"log/slog"
	"mi-restful-api/utils"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func captureSlogDebugOutput(f func()) string {
	var buf bytes.Buffer
	h := slog.NewTextHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug})
	logger := slog.New(h)
	slog.SetDefault(logger)
	f()
	return buf.String()
}

func TestDebugLogMessageWithValidInputs(t *testing.T) {
	hdcpIndex := 12.3
	slopeRating := 113
	courseRating := 72.5
	par := 72
	hdcpAllowance := 0.85

	output := captureSlogDebugOutput(func() {
		slog.Debug("ComputeHdcp input", "hdcpIndex", hdcpIndex, "slopeRating", slopeRating, "courseRating", courseRating, "par", par, "hdcpAllowance", hdcpAllowance)
	})

	if !strings.Contains(output, "ComputeHdcp input") {
		t.<PERSON><PERSON><PERSON>("Expected log message to contain 'ComputeHdcp input', got: %s", output)
	}
	if !strings.Contains(output, "hdcpIndex=12.3") {
		t.Errorf("Expected log message to contain hdcpIndex=12.3, got: %s", output)
	}
	if !strings.Contains(output, "slopeRating=113") {
		t.Errorf("Expected log message to contain slopeRating=113, got: %s", output)
	}
	if !strings.Contains(output, "courseRating=72.5") {
		t.Errorf("Expected log message to contain courseRating=72.5, got: %s", output)
	}
	if !strings.Contains(output, "par=72") {
		t.Errorf("Expected log message to contain par=72, got: %s", output)
	}
	if !strings.Contains(output, "hdcpAllowance=0.85") {
		t.Errorf("Expected log message to contain hdcpAllowance=0.85, got: %s", output)
	}
}

func TestDebugLogCalledOnComputeHdcp(t *testing.T) {
	// Simulate ComputeHdcp invocation
	output := captureSlogDebugOutput(func() {
		slog.Debug("ComputeHdcp input", "hdcpIndex", 10.0, "slopeRating", 120, "courseRating", 70.0, "par", 72, "hdcpAllowance", 1.0)
	})
	if !strings.Contains(output, "ComputeHdcp input") {
		t.Errorf("Expected debug log to be called during ComputeHdcp, got: %s", output)
	}
}

func TestDebugLogIncludesAllParameters(t *testing.T) {
	output := captureSlogDebugOutput(func() {
		slog.Debug("ComputeHdcp input", "hdcpIndex", 5.5, "slopeRating", 130, "courseRating", 68.0, "par", 71, "hdcpAllowance", 0.9)
	})
	params := []string{"hdcpIndex=5.5", "slopeRating=130", "courseRating=68", "par=71", "hdcpAllowance=0.9"}
	for _, param := range params {
		if !strings.Contains(output, param) {
			t.Errorf("Expected log message to contain %s, got: %s", param, output)
		}
	}
}

func TestDebugLogHandlesNilInputs(t *testing.T) {
	var hdcpIndex interface{} = nil
	var slopeRating interface{} = nil
	var courseRating interface{} = nil
	var par interface{} = nil
	var hdcpAllowance interface{} = nil

	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Logging with nil inputs caused panic: %v", r)
		}
	}()
	output := captureSlogDebugOutput(func() {
		slog.Debug("ComputeHdcp input", "hdcpIndex", hdcpIndex, "slopeRating", slopeRating, "courseRating", courseRating, "par", par, "hdcpAllowance", hdcpAllowance)
	})
	if !strings.Contains(output, "hdcpIndex=<nil>") {
		t.Errorf("Expected log message to handle nil hdcpIndex, got: %s", output)
	}
}

func TestComputePar_EmptyReturns72(t *testing.T) {
	assert.Equal(t, "72", utils.ComputePar(""))
}

func TestComputePar_AllFours18Holes(t *testing.T) {
	// 18 holes, par 4 each => 72
	parStr := "444444444444444444"
	assert.Equal(t, "72", utils.ComputePar(parStr))
}

func TestComputePar_SimpleSequence(t *testing.T) {
	assert.Equal(t, "15", utils.ComputePar("54321")) // 5+4+3+2+1 = 15
	assert.Equal(t, "45", utils.ComputePar("123456789"))
}

func TestComputePar_AllZeros(t *testing.T) {
	parStr := "000000000000000000"
	assert.Equal(t, "0", utils.ComputePar(parStr))
}

func TestComputePar_Prod_Case(t *testing.T) {
	parStr := "434454534443454354"
	assert.Equal(t, "72", utils.ComputePar(parStr))
}

func TestComputePar_MixedDigits(t *testing.T) {
	// 5+4+4+3+4 = 20
	assert.Equal(t, "20", utils.ComputePar("54434"))
}
