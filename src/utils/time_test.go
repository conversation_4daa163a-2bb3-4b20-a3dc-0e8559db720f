package utils_test

import (
	"mi-restful-api/utils"
	"testing"
	"time"
)

func TestStringToTime(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		wantErr  bool
		wantTime time.Time
	}{
		{
			name:     "RFC3339 format",
			input:    "2024-03-15T10:30:00Z",
			wantErr:  false,
			wantTime: time.Date(2024, 3, 15, 10, 30, 0, 0, time.UTC),
		},
		{
			name:     "Custom format with timezone",
			input:    "2024-03-15 10:30:00+09:00",
			wantErr:  false,
			wantTime: time.Date(2024, 3, 15, 10, 30, 0, 0, time.FixedZone("", 9*60*60)),
		},
		{
			name:    "Invalid format",
			input:   "2024-03-15 10:30:00",
			wantErr: true,
		},
		{
			name:    "Empty string",
			input:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := utils.StringToTime(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("StringToTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !got.Equal(tt.wantTime) {
				t.Errorf("StringToTime() = %v, want %v", got, tt.wantTime)
			}
		})
	}
}

func TestTimeToString(t *testing.T) {
	tests := []struct {
		name  string
		input time.Time
		want  string
	}{
		{
			name:  "UTC time",
			input: time.Date(2024, 3, 15, 10, 30, 0, 0, time.UTC),
			want:  "2024-03-15T10:30:00Z",
		},
		{
			name:  "JST time",
			input: time.Date(2024, 3, 15, 10, 30, 0, 0, time.FixedZone("JST", 9*60*60)),
			want:  "2024-03-15T10:30:00+09:00",
		},
		{
			name:  "Zero time",
			input: time.Time{},
			want:  "0001-01-01T00:00:00Z",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := utils.TimeToString(tt.input); got != tt.want {
				t.Errorf("TimeToString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTimeToStringByLayout(t *testing.T) {
	testTime := time.Date(2024, 3, 15, 10, 30, 0, 0, time.UTC)

	tests := []struct {
		name   string
		input  time.Time
		layout string
		want   string
	}{
		{
			name:   "RFC3339 layout",
			input:  testTime,
			layout: time.RFC3339,
			want:   "2024-03-15T10:30:00Z",
		},
		{
			name:   "Date only",
			input:  testTime,
			layout: "2006-01-02",
			want:   "2024-03-15",
		},
		{
			name:   "Custom format",
			input:  testTime,
			layout: "2006/01/02 15:04:05",
			want:   "2024/03/15 10:30:00",
		},
		{
			name:   "Japanese format",
			input:  testTime,
			layout: "2006年01月02日 15時04分",
			want:   "2024年03月15日 10時30分",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := utils.TimeToStringByLayout(tt.input, tt.layout); got != tt.want {
				t.Errorf("TimeToStringByLayout() = %v, want %v", got, tt.want)
			}
		})
	}
}
