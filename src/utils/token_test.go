package utils_test

import (
	"encoding/json"
	"log/slog"
	"mi-restful-api/model"
	"testing"
)

// func TestGenerateToken(t *testing.T) {
// 	raw := `a:1:{s:12:"focused_user";a:1:{s:10:"attributes";a:9:{s:7:"user_id";s:10:"UserIdTest";s:7:"account";s:11:"AccountTest";s:8:"password";s:12:"PasswordTest";s:9:"user_name";s:12:"UserNameTest";s:14:"user_name_kana";s:16:"UserNameKanaTest";s:5:"admin";s:9:"AdminTest";s:9:"office_id";s:12:"OfficeIdTest";s:10:"created_at";s:13:"CreatedAtTest";s:10:"updated_at";s:13:"UpdatedAtTest";}}}`
// 	// var u string
// 	// if err := phpserialize.Unmarshal([]byte(raw), &u); err != nil {
// 	// 	// t.Fatalf(`Unmarshal("raw") = %q, want "success", error`, err)
// 	// 	panic(err)
// 	// }
// 	// slog.Info(u)

// 	var v map[any]any
// 	if err := phpserialize.Unmarshal([]byte(raw), &v); err != nil {
// 		// t.Fatalf(`Unmarshal("raw") = %q, want "success", error`, err)
// 		panic(err)
// 	}

// 	slog.Info(v)
// }

func TestSessionData(t *testing.T) {

	// {
	// 	_previous: { url: 'http://localhost:8000/master/office' },
	// 	flash: { old: [], new: [] },
	// 	_token: 'mR7ZSlMQDzIKDXtFBrpB7YbATze3NiyHXhkIMZtV',
	// 	login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d: 1,
	// 	focused_user: {
	// 	  dates: [ 'deleted_at' ],
	// 	  table: 'accounts',
	// 	  primaryKey: 'user_id',
	// 	  guarded: [ 'user_id' ],
	// 	  casts: { password: 'string' },
	// 	  hidden: [ 'password', 'remember_token' ],
	// 	  connection: null,
	// 	  keyType: 'int',
	// 	  perPage: 15,
	// 	  incrementing: true,
	// 	  timestamps: true,
	// 	  attributes: {
	// 		user_id: '1',
	// 		account: 'sysadmin',
	// 		password: '937b3cc48d225bb22e7c649f76d1dcbc8eeb04d4987d55963073db140cca891f',
	// 		user_name: 'システム管理者',
	// 		user_name_kana: 'しすてむかんりしゃ',
	// 		admin: '1',
	// 		office_id: '1',
	// 		remember_token: 'ltXOtDrDoTKdAO9KOiZMcQofbzhDQtmaTag6g81QLMdWeeGN7Y1B9cnSxZbF',
	// 		temp_password_state: '0',
	// 		temp_password_expire: null,
	// 		officeuser: '0',
	// 		password_change_date: null,
	// 		created_user_id: '0',
	// 		created_at: null,
	// 		updated_user_id: null,
	// 		updated_at: '2024-08-06 12:12:58',
	// 		deleted_user_id: null,
	// 		deleted_at: null
	// 	  },
	// 	  original: {
	// 		user_id: '1',
	// 		account: 'sysadmin',
	// 		password: '937b3cc48d225bb22e7c649f76d1dcbc8eeb04d4987d55963073db140cca891f',
	// 		user_name: 'システム管理者',
	// 		user_name_kana: 'しすてむかんりしゃ',
	// 		admin: '1',
	// 		office_id: '1',
	// 		remember_token: 'ltXOtDrDoTKdAO9KOiZMcQofbzhDQtmaTag6g81QLMdWeeGN7Y1B9cnSxZbF',
	// 		temp_password_state: '0',
	// 		temp_password_expire: null,
	// 		officeuser: '0',
	// 		password_change_date: null,
	// 		created_user_id: '0',
	// 		created_at: null,
	// 		updated_user_id: null,
	// 		updated_at: '2024-08-06 12:12:58',
	// 		deleted_user_id: null,
	// 		deleted_at: null
	// 	  },
	// 	  relations: { office: [Object] },
	// 	  visible: [],
	// 	  appends: [],
	// 	  fillable: [],
	// 	  dateFormat: null,
	// 	  touches: [],
	// 	  observables: [],
	// 	  with: [],
	// 	  morphClass: null,
	// 	  exists: true,
	// 	  wasRecentlyCreated: false,
	// 	  forceDeleting: false
	// 	},
	// 	_sf2_meta: { u: **********, c: **********, l: '0' }
	//   }

	//sessionData.focused_user.attributes.user_id

	var sessionData = model.SessionData{
		UserId:       "UserIdTest",
		Account:      "AccountTest",
		Password:     "PasswordTest",
		UserName:     "UserNameTest",
		UserNameKana: "UserNameKanaTest",
		Admin:        "AdminTest",
		OfficeId:     "OfficeIdTest",
		CreatedAt:    "CreatedAtTest",
		UpdatedAt:    "UpdatedAtTest",
	}

	// var temp, _ = phpserialize.Marshal(sessionData)

	var data, _ = json.Marshal(sessionData)

	var raw = string(data)
	slog.Info(raw)

	// var tempV any

	// if err := phpserialize.Unmarshal([]byte(raw), &tempV); err != nil {
	// 	// t.Fatalf(`Unmarshal("raw") = %q, want "success", error`, err)
	// 	panic(err)
	// }

	var v model.SessionData

	if err := json.Unmarshal([]byte(raw), &v); err != nil {
		// t.Fatalf(`Unmarshal("raw") = %q, want "success", error`, err)
		panic(err)
	}

	slog.Info(v.UserId)

	if v.UserId != sessionData.UserId {
		t.Fatalf(`UserId should equal`)
	}

}

// func TestSetToken(t *testing.T) {
// 	var redisInstance *redis.Client
// 	var redisOnce sync.Once
// 	redisOnce.Do(func() {
// 		redisInstance = redis.NewClient(&redis.Options{
// 			Addr:               "127.0.0.1:6379",
// 			Password:           "",
// 			DB:                 0,
// 			PoolSize:           1,
// 			MinIdleConns:       0,
// 			MaxConnAge:         0,
// 			PoolTimeout:        0,
// 			IdleTimeout:        0,
// 			IdleCheckFrequency: 0,
// 			TLSConfig:          nil,
// 			Limiter:            nil,
// 		})
// 	})
// 	var authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOlszNiwyMTgsMjM0LDQyLDIyLDYxLDgsMTU4LDI0NSwyOCwxNzMsMTY4LDU4LDEyNywxNjYsMTk4LDU4LDE1NywyMjMsMjEzXSwidGltZXN0YW1wcyI6MTIzMTIzMTIzLCJ1c2VyQWdlbnQiOiJpbnNvbW5pYS85LjMuMyJ9.jyZGQMdGZ-cD02NCAeYZpmgyTcgjbDQ2nr4MyWbiB10"

// 	ctx := context.Background()

// 	var sessionDataForTest = "test_data"
// 	if _, err := redisInstance.SetEX(ctx, "authToken:"+authToken, "test_data", time.Second*time.Duration(60)).Result(); err != nil {
// 		panic(err)
// 	}

// 	sessionData, err := redisInstance.GetDel(ctx, "authToken:"+authToken).Result()
// 	if err != nil {
// 		t.Fatalf(`GetDel("authToken") = %q, want "not nil", error`, err)
// 	}
// 	if sessionData != sessionDataForTest {
// 		t.Fatalf(`GetDel("authToken") = %q, %v, want "", error`, sessionDataForTest, err)
// 	}

// 	var testSessionData = `a:1:{s:12:"focused_user";a:1:{s:10:"attributes";a:9:{s:7:"user_id";s:10:"UserIdTest";s:7:"account";s:11:"AccountTest";s:8:"password";s:12:"PasswordTest";s:9:"user_name";s:12:"UserNameTest";s:14:"user_name_kana";s:16:"UserNameKanaTest";s:5:"admin";s:9:"AdminTest";s:9:"office_id";s:12:"OfficeIdTest";s:10:"created_at";s:13:"CreatedAtTest";s:10:"updated_at";s:13:"UpdatedAtTest";}}}`

// 	if _, err := redisInstance.SetEX(ctx, "session_id_123", testSessionData, time.Minute*time.Duration(60)).Result(); err != nil {
// 		panic(err)
// 	}

// }
