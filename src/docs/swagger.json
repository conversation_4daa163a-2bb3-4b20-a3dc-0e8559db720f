{"swagger": "2.0", "info": {"description": "This is a dev server.", "title": "mi-restful-api", "contact": {}, "version": "1.0"}, "host": "localhost:888", "basePath": "/api", "paths": {"/app/answer": {"post": {"description": "create a score record", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Questionnaire"], "summary": "新しい評価を追加", "operationId": "post-score-store", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.QuestionnaireCreateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}, "603": {"description": "weekday deal error", "schema": {"$ref": "#/definitions/response.QuestionnaireCreateResp"}}}}}, "/app/caddyon": {"get": {"description": "キャディ評価機能有効", "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "キャディ評価機能有効", "operationId": "get-caddy_on-index", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.CaddyOnResp"}}}}}, "/app/cartinfo": {"get": {"description": "カート情報取得", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Questionnaire"], "summary": "カート情報取得", "operationId": "get-cartinfo", "parameters": [{"type": "integer", "default": 23, "description": "カートID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}}}}, "/app/cartinfo/v2": {"get": {"description": "カート情報取得 v2", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Questionnaire"], "summary": "カート情報取得 v2", "operationId": "get-cartinfo-v2", "parameters": [{"type": "integer", "default": 23, "description": "カートID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.CartInfoResp"}}}}}, "/app/evaluation": {"get": {"description": "評価設置リストを取得", "produces": ["application/json"], "tags": ["Evaluation"], "summary": "評価設置リストを取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}}}}, "/app/office/barcodetolocker": {"get": {"description": "バーコードからロッカーキーに変換", "produces": ["application/json"], "tags": ["OfficeSettings"], "summary": "バーコードからロッカーキーに変換", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.BarcodeToLockerResp"}}}}}, "/app/office/settings": {"get": {"description": "システム設定取得", "produces": ["application/json"], "tags": ["OfficeSettings"], "summary": "システム設定取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}}}}, "/app/online-compe/:compeNo": {"get": {"description": "get compe by no", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get compe by no", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}}}}, "/app/online-compe/leaderboard/ranking/:compeNo": {"get": {"description": "get leaderboard ranking コンペ一覧・リーダボード", "tags": ["OnlineCompe"], "summary": "get leaderboard ranking", "parameters": [{"type": "string", "description": "data str handy or peoria", "name": "aggregation_type", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}}}}, "/app/online-compe/leaderboard/ranking/type/:compeNo": {"get": {"description": "get leaderboard ranking コンペ一覧・リーダボード Type", "tags": ["OnlineCompe"], "summary": "get leaderboard ranking type", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}}}}, "/app/online-compe/player-compe/:playerNo": {"get": {"description": "get player compes 参加者 コンペ詳細", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get player compes", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompeJoinedPlayersReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}}}}, "/app/quesnairesettings": {"get": {"description": "アンケート各種設定取得", "produces": ["application/json"], "tags": ["QuesnaireSettings"], "summary": "アンケート各種設定取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}}}}, "/app/question": {"get": {"description": "問題設置リストを取得", "produces": ["application/json"], "tags": ["Question"], "summary": "問題設置リストを取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}}}}, "/app/start-guidance/cart-info": {"get": {"description": "CartInfo (ByLockerNo)", "produces": ["application/json"], "tags": ["TeeTime"], "summary": "CartInfo (ByLockerNo)", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/startguidance.CartInfoReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/startguidance.CartInfoResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/startguidance.CartInfoResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/startguidance.CartInfoResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/startguidance.CartInfoResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/startguidance.CartInfoResp"}}}}}, "/app/start-guidance/info": {"get": {"description": "スタート案内インフォメーション取得", "produces": ["application/json"], "tags": ["StartGuideInfo"], "summary": "スタート案内インフォメーション取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.StartGuideInfoIndexResp"}}}}}, "/ping": {"get": {"description": "get ping", "produces": ["application/json"], "tags": ["<PERSON>"], "summary": "server ping", "operationId": "get-string", "responses": {"200": {"description": "pong", "schema": {"$ref": "#/definitions/response.Ping"}}}}}, "/web/answer/lowest": {"get": {"description": "統計問題の最低点", "produces": ["application/json"], "tags": ["Analysis"], "summary": "統計問題の最低点", "operationId": "get-answer-lowest", "parameters": [{"type": "string", "default": "\"2024-07\"", "description": "開始年月", "name": "start_month", "in": "query", "required": true}, {"type": "string", "default": "\"2024-07\"", "description": "終了年月", "name": "end_month", "in": "query", "required": true}, {"type": "integer", "description": "キャティID", "name": "caddy_id", "in": "query"}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}}}}, "/web/caddy": {"get": {"description": "キャディ評価", "produces": ["application/json"], "tags": ["Analysis"], "summary": "キャディ評価", "operationId": "get-answer-caddy", "parameters": [{"type": "string", "default": "\"2024-07\"", "description": "開始年月", "name": "start_month", "in": "query", "required": true}, {"type": "string", "default": "\"2024-07\"", "description": "終了年月", "name": "end_month", "in": "query", "required": true}, {"type": "integer", "description": "キャティID", "name": "caddy_id", "in": "query"}, {"type": "integer", "default": 3, "description": "曜日[ 1 平日， 2 周末， 3 未指定]", "name": "weekday", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.CaddyAnalysisResp"}}}}}, "/web/caddylist": {"get": {"description": "キャディ一覧", "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "キャディ一覧", "operationId": "get-caddy-index", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.CaddyIndexResp"}}}}}, "/web/evaluation": {"get": {"description": "評価設置リストを取得", "produces": ["application/json"], "tags": ["Evaluation"], "summary": "評価設置リストを取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.EvaluationIndexResp"}}}}, "put": {"description": "update comment", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Evaluation"], "summary": "評価内容またスコアを更新", "operationId": "put-comment-update", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/request.EvaluationPutReq"}}}], "responses": {"200": {"description": "not config response", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.EvaluationUpdateResp"}}}}}, "/web/office/settings": {"get": {"description": "システム設定取得", "produces": ["application/json"], "tags": ["OfficeSettings"], "summary": "システム設定取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.OfficeSettingsIndexResp"}}}}, "post": {"description": "システム設定保存", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OfficeSettings"], "summary": "システム設定保存", "operationId": "post-officesettings-store", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.OfficeSettingsCreateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.OfficeSettingsCreateResp"}}}}}, "/web/online-compe/:compeNo": {"get": {"description": "get compe by no", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get compe by no", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.OnlineCompeResp"}}}}}, "/web/online-compe/compe-player/:compeNo/list": {"get": {"description": "get compe player list コンペ詳細・参加者", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get compe player list", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/player.JoinedPlayesResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/player.JoinedPlayesResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/player.JoinedPlayesResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/player.JoinedPlayesResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/player.JoinedPlayesResp"}}}}}, "/web/online-compe/compe-player/:compeNo/update": {"post": {"description": "update compe players コンペ詳細・参加者, also remove player which is not in the request", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "update compe players", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompeJoinedPlayersReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/online-compe/course/list": {"get": {"description": "get course list", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get course list", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/course.CoursesResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/course.CoursesResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/course.CoursesResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/course.CoursesResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/course.CoursesResp"}}}}}, "/web/online-compe/create": {"put": {"description": "create compe", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "create compe", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompeCreationReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/online-compe/default-setting": {"get": {"description": "get compe default setting", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get compe default setting", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.OnlineCompeDefaultSettingResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.OnlineCompeDefaultSettingResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.OnlineCompeDefaultSettingResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.OnlineCompeDefaultSettingResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.OnlineCompeDefaultSettingResp"}}}}}, "/web/online-compe/default-setting/update": {"post": {"description": "update compe default setting", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "update compe default setting", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompeDefaultSettingReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/online-compe/img/upload/:compeNo": {"post": {"description": "upload img to s3", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "upload img to s3", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.UploadImgResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.UploadImgResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.UploadImgResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.UploadImgResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.UploadImgResp"}}}}}, "/web/online-compe/join": {"post": {"description": "Join<PERSON><PERSON>pe compe", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "Join<PERSON><PERSON>pe compe", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompePlayerJoinReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/online-compe/latest-no": {"get": {"description": "get latest avaliable compe id", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get latest avaliable compe id", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.OnlineCompeLatestNoResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.OnlineCompeLatestNoResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.OnlineCompeLatestNoResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.OnlineCompeLatestNoResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.OnlineCompeLatestNoResp"}}}}}, "/web/online-compe/leaderboard/ranking/:compeNo": {"get": {"description": "get leaderboard ranking コンペ一覧・リーダボード", "tags": ["OnlineCompe"], "summary": "get leaderboard ranking", "parameters": [{"type": "string", "description": "data str handy or peoria", "name": "aggregation_type", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}}}}, "/web/online-compe/leaderboard/ranking/share": {"post": {"description": "get leaderboard ranking コンペ一覧・リーダボード", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get leaderboard ranking", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/online-compe/leaderboard/ranking/share-key/create": {"post": {"description": "create leaderboard ranking share key", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "create leaderboard ranking share key", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareKeyCreateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareKeyCreateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareKeyCreateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareKeyCreateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareKeyCreateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderboardRankingShareKeyCreateResp"}}}}}, "/web/online-compe/leaderboard/ranking/shared/:compeNo": {"get": {"description": "get leaderboard ranking コンペ一覧・リーダボード", "tags": ["OnlineCompe"], "summary": "get leaderboard ranking by share key", "parameters": [{"type": "string", "description": "data str handy or peoria", "name": "aggregation_type", "in": "query", "required": true}, {"type": "string", "description": "compe no", "name": "compe_no", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingResp"}}}}}, "/web/online-compe/leaderboard/ranking/type/:compeNo": {"get": {"description": "get leaderboard ranking コンペ一覧・リーダボード Type", "tags": ["OnlineCompe"], "summary": "get leaderboard ranking type", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}}}}, "/web/online-compe/leaderboard/ranking/type/shared/:compeNo": {"get": {"description": "get leaderboard ranking コンペ一覧・リーダボード Type", "tags": ["OnlineCompe"], "summary": "get leaderboard ranking type by sharekey", "parameters": [{"type": "string", "description": "compe no", "name": "compe_no", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.LeaderBoardRankingTypeResp"}}}}}, "/web/online-compe/office-compe/list": {"get": {"description": "get office compe list コンペ一覧", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get office compe list", "parameters": [{"type": "string", "description": "競技方法  handy, peoria", "name": "compe_kind", "in": "query"}, {"type": "integer", "description": "コンペタイプ 0 team(for phase 2) 1 個人戦", "name": "compe_type", "in": "query"}, {"type": "string", "description": "key word in the compe name filter by this word", "name": "free_word", "in": "query"}, {"minimum": 100, "type": "integer", "description": "limit more than 100", "name": "limit", "in": "query", "required": true}, {"type": "string", "description": "オフィスキー", "name": "office_key", "in": "query"}, {"minimum": 0, "type": "integer", "description": "offset >=0", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "開催日 eg. 20060102", "name": "play_date", "in": "query"}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.OnlineCompeOfficeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.OnlineCompeOfficeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.OnlineCompeOfficeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.OnlineCompeOfficeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.OnlineCompeOfficeResp"}}}}}, "/web/online-compe/player-compe/:playerNo": {"get": {"description": "get player compes 参加者 コンペ詳細", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get player compes", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompeJoinedPlayersReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/player.PlayerCompeResp"}}}}}, "/web/online-compe/player-info/search": {"get": {"description": "search player info コンペ詳細・参加者", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "search player info", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.SearchPlayerInfoReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/player.PlayerInfoResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/player.PlayerInfoResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/player.PlayerInfoResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/player.PlayerInfoResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/player.PlayerInfoResp"}}}}}, "/web/online-compe/tee/info": {"get": {"description": "get TeeInfo コンペ詳細・参加者", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get TeeInfo", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/tee.TeeInfoResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/tee.TeeInfoResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/tee.TeeInfoResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/tee.TeeInfoResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/tee.TeeInfoResp"}}}}}, "/web/online-compe/tee/sheet": {"get": {"description": "get TeeSheet", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get TeeSheet", "parameters": [{"type": "string", "description": "data str like 20200101", "name": "date_str", "in": "query"}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/tee.TeeSheetResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/tee.TeeSheetResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/tee.TeeSheetResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/tee.TeeSheetResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/tee.TeeSheetResp"}}}}}, "/web/online-compe/tee/sheet/player/search": {"get": {"description": "get TeeInfo コンペ詳細・参加者", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "get player from tee", "parameters": [{"type": "string", "description": "birthday 1990-01-01", "name": "birthday", "in": "query", "required": true}, {"type": "string", "description": "play_date 20230101 , default should be today", "name": "play_date", "in": "query"}, {"type": "string", "description": "<PERSON><PERSON><PERSON>", "name": "player_name", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/tee.TeePlayerResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/tee.TeePlayerResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/tee.TeePlayerResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/tee.TeePlayerResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/tee.TeePlayerResp"}}}}}, "/web/online-compe/tee/sheet/player/update": {"post": {"description": "update TeeSheet  by date and cart no", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "update TeeSheet by date and cart no", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tee.TeeSheetPlayerUpdateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/online-compe/update": {"post": {"description": "update compe", "produces": ["application/json"], "tags": ["OnlineCompe"], "summary": "update compe", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/compe.OnlineCompeUpdateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/compe.BasicResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/compe.BasicResp"}}}}}, "/web/quesnairesettings": {"get": {"description": "アンケート各種設定取得", "produces": ["application/json"], "tags": ["QuesnaireSettings"], "summary": "アンケート各種設定取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsIndexResp"}}}}, "post": {"description": "create a score record", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["QuesnaireSettings"], "summary": "アンケート各種設定を追加・編集", "operationId": "post-quesnairesettings-store", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.QuesnaireSettingsCreateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuesnaireSettingsCreateResp"}}}}}, "/web/question": {"get": {"description": "問題設置リストを取得", "produces": ["application/json"], "tags": ["Question"], "summary": "問題設置リストを取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionIndexResp"}}}}, "post": {"description": "新しい問題設置を作成", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Question"], "summary": "新しい問題設置を作成", "operationId": "get-question-store", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.Question"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionCreateResp"}}}}}, "/web/question/:id": {"put": {"description": "問題内容とソートIDを更新", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Question"], "summary": "問題内容を更新", "operationId": "put-question-update", "parameters": [{"type": "string", "description": "问题id", "name": "id", "in": "path", "required": true}, {"description": "設問内容", "name": "content", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}}}}, "/web/question/{id}": {"delete": {"description": "delete question", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Question"], "summary": "question delete", "operationId": "get-question-delete", "parameters": [{"type": "string", "description": "設問ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionDelResp"}}}}}, "/web/questionindex": {"put": {"description": "問題内容とソートIDを更新", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Question"], "summary": "問題内容を更新", "operationId": "put-question-index", "parameters": [{"description": "設問ID", "name": "id", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "設問順番", "name": "index", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.UpdateIndexParams"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionShowResp"}}}}}, "/web/questionnaire": {"get": {"description": "評価リスト", "produces": ["application/json"], "tags": ["Questionnaire"], "summary": "評価リスト", "operationId": "get-score-list", "parameters": [{"description": "Request json data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.QuestionnaireIndexReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionnaireIndexResp"}}}}}, "/web/questionnaire/csv": {"get": {"description": "create a score record", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Questionnaire"], "summary": "新しい評価を追加", "operationId": "get-questionnaire-csv", "responses": {"200": {"description": "response", "schema": {"type": "string"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "604": {"description": "csv header write error", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}, "605": {"description": "csv data write error", "schema": {"$ref": "#/definitions/response.QuestionnaireExportCsv"}}}}}, "/web/start-guidance/settings": {"get": {"description": "スタート案内設定取得", "produces": ["application/json"], "tags": ["StartGuideSettings"], "summary": "スタート案内設定取得", "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsIndexResp"}}}}, "post": {"description": "スタート案内設定を保存", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["StartGuideSettings"], "summary": "スタート案内設定を保存", "operationId": "post-startguidesettings-store", "parameters": [{"description": "Request payload", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.StartGuideSettingsCreateReq"}}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.StartGuideSettingsCreateResp"}}}}}, "/web/statistical": {"get": {"description": "アンケート統計", "produces": ["application/json"], "tags": ["Analysis"], "summary": "アンケート統計", "operationId": "get-answer-statistical", "parameters": [{"type": "string", "default": "\"2024-07\"", "description": "開始年月", "name": "start_month", "in": "query", "required": true}, {"type": "string", "default": "\"2024-07\"", "description": "終了年月", "name": "end_month", "in": "query", "required": true}, {"type": "integer", "description": "キャティID", "name": "caddy_id", "in": "query"}, {"type": "integer", "default": 3, "description": "曜日[ 1 平日， 2 周末， 3 未指定]", "name": "weekday", "in": "query", "required": true}], "responses": {"200": {"description": "response", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "400": {"description": "Bad Request ,param error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "601": {"description": "mysql conn error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}, "602": {"description": "mysql sql error", "schema": {"$ref": "#/definitions/response.StatisticalResp"}}}}}}, "definitions": {"compe.Basic": {"type": "object", "required": ["compe_name", "compe_no", "duration", "organizer", "participation_fee", "promotional_image", "target_office_type"], "properties": {"compe_name": {"description": "コンペ名", "type": "string"}, "compe_no": {"description": "コンペNo", "type": "integer", "minimum": 1}, "duration": {"description": "開催日", "type": "object", "required": ["from", "to"], "properties": {"from": {"description": "ISO 8601 or date type", "type": "string"}, "to": {"description": "after from also should after now", "type": "string"}}}, "old_compe": {"type": "object", "properties": {"old_compe_no": {"type": "string"}, "old_compe_office_key": {"type": "string"}, "old_compe_start_time": {"type": "string"}}}, "organizer": {"description": "主催者", "type": "string"}, "participation_fee": {"description": "参加料金 円", "type": "integer"}, "promotional_image": {"description": "宣伝画像登録", "type": "string"}, "target_office": {"description": "自コースのオフィスKEY", "type": "string"}, "target_office_list": {"description": "グループコース", "type": "array", "items": {"type": "string"}}, "target_office_type": {"description": "0 自コース 1 グループコース", "type": "integer"}}}, "compe.BasicResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー 500 内部エラー", "type": "integer"}, "message": {"description": "message メッセージ", "type": "string"}}}, "compe.BreakingNews": {"type": "object", "properties": {"player_no": {"type": "string"}, "score": {"type": "string"}}}, "compe.CompeSetting": {"type": "object", "required": ["entry_from_navi", "prize_setting", "ranking_aggregation", "round"], "properties": {"entry_from_navi": {"description": "ナビからエントリー 0 許可しない 1許可する", "type": "integer"}, "prize_setting": {"description": "入賞設定", "type": "array", "items": {"type": "object", "required": ["type"], "properties": {"name": {"type": "string"}, "order": {"type": "object", "additionalProperties": {"type": "integer"}}, "setting": {"type": "object", "additionalProperties": {"type": "integer"}}, "type": {"type": "string"}}}}, "ranking_aggregation": {"description": "ランキング集計 0ホールを隠す 1最終3ホールを隠す 2最終6ホールを隠す 3最終9ホールを隠す", "type": "integer"}, "round": {"description": "0.5ラウンド 1ラウンド 1.5ラウンド", "type": "string"}}}, "compe.CompeTypeSetting": {"type": "object", "required": ["type"], "properties": {"handy": {"description": "ハンディ optional item for 個人戦", "type": "object", "required": ["distribution", "handicap", "net_computation_type", "ranking_order"], "properties": {"distribution": {"description": "0 配信しない 1 配信する", "type": "integer"}, "handicap": {"description": "使用ハンディキャップ", "type": "object", "required": ["hdcp_allowance", "hdcp_date", "type"], "properties": {"hdcp_allowance": {"description": "HDCPの許容値 0-100 (%)", "type": "integer", "maximum": 100, "minimum": 0}, "hdcp_date": {"description": "時点のHDCPを使う", "type": "string"}, "type": {"description": "0 HDCP Index(WHS) 1 プライベートハンディキャップ", "type": "integer"}}}, "net_computation_type": {"description": "NET計算方法 0 HDCPナンバーで割り振り 1 按分方式", "type": "integer"}, "ranking_order": {"description": "同点時の優先順位", "type": "object", "additionalProperties": {"type": "string"}}}}, "peoria": {"description": "ぺリア optional item for 個人戦", "type": "object", "required": ["aggregation_method", "distribution", "handicap_upper_limit", "par_limit", "ranking_order"], "properties": {"aggregation_method": {"description": "集計方法", "type": "object", "required": ["type"], "properties": {"type": {"description": "0 ペリア(6H)、1 新ペリア(12H)、2 新新ペリア(9H)", "type": "integer"}}}, "distribution": {"description": "0 配信しない 1 配信する", "type": "integer"}, "handicap_upper_limit": {"description": "ハンデ上限設定", "type": "object", "properties": {"men": {"description": "men hdcp max", "type": "integer"}, "women": {"description": "women hdcp max", "type": "integer"}}}, "par_limit": {"description": "打数制限", "type": "object", "required": ["type"], "properties": {"par_n": {"description": "?", "type": "integer"}, "par_x": {"description": "PAR×N+X", "type": "integer"}, "type": {"description": "0 制限なし、1 PAR×2、2 PAR×2-1、3 PAR×3、4 PAR×3-1、　5  PAR+X、6 X、7 PAR×N+X　から選択。", "type": "integer"}}}, "ranking_order": {"description": "同点時の優先順位", "type": "object", "additionalProperties": {"type": "string"}}}}, "type": {"description": "0 team 1 個人戦", "type": "integer"}}}, "compe.DefaultCompeSetting": {"type": "object", "properties": {"entry_from_navi": {"description": "ナビからエントリー 0 許可しない 1許可する", "type": "integer"}, "ranking_aggregation": {"description": "ランキング集計 0ホールを隠す 1最終3ホールを隠す 2最終6ホールを隠す 3最終9ホールを隠す", "type": "integer"}, "round": {"type": "string"}}}, "compe.DefaultHandicap": {"type": "object", "properties": {"type": {"description": "ハンディキャップSetting 0 HDCP Index(WHS) 1 フロント連携HDCP", "type": "integer"}}}, "compe.DefaultOtherSetting": {"type": "object", "properties": {"leadboard_change": {"description": "リーダーボード切替", "type": "object", "properties": {"default": {"description": "?", "type": "string"}, "type": {"description": "0 しない 1 自由設定", "type": "integer"}}}, "marker_setting": {"description": "マーカー設定 0 しない 1 マーカーあり", "type": "integer"}}}, "compe.HiddenHoleSetting": {"type": "object", "required": ["course_index", "course_name", "hidden_hole_index"], "properties": {"course_index": {"description": "コースインデックス", "type": "string"}, "course_name": {"description": "コース名", "type": "string"}, "hidden_hole_index": {"description": "隠しホールインデックス", "type": "array", "items": {"type": "integer"}}}}, "compe.Holes": {"type": "object", "properties": {"hole_index": {"type": "string"}, "used_hdcp": {"type": "string"}, "used_par": {"type": "string"}}}, "compe.LeaderBoardRankingResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"breaking_news": {"type": "array", "items": {"$ref": "#/definitions/compe.BreakingNews"}}, "compe_no": {"type": "string"}, "courses": {"type": "array", "items": {"$ref": "#/definitions/compe.ScoreCourses"}}, "play_date": {"type": "string"}, "rankings": {"type": "array", "items": {"$ref": "#/definitions/compe.PlayerRankings"}}, "updated_at": {"type": "string"}}}, "message": {"type": "string"}}}, "compe.LeaderBoardRankingTypeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"ranking_type": {"type": "array", "items": {"type": "string"}}}}, "message": {"type": "string"}}}, "compe.LeaderboardRankingShareKeyCreateReq": {"type": "object", "required": ["compe_no"], "properties": {"compe_no": {"type": "integer", "minimum": 1}, "share_key": {"description": "optinal size should be 6", "type": "string"}}}, "compe.LeaderboardRankingShareKeyCreateResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"share_key": {"type": "string"}}}, "message": {"type": "string"}}}, "compe.LeaderboardRankingShareReq": {"type": "object", "required": ["compe_no", "email", "share_key"], "properties": {"compe_no": {"type": "integer", "minimum": 1}, "email": {"type": "string"}, "share_key": {"type": "string"}}}, "compe.MergedCompe": {"type": "object", "properties": {"aggregation_types": {"description": "競技方法list", "type": "array", "items": {"type": "string"}}, "compe_name": {"type": "string"}, "compe_no": {"type": "integer"}, "compe_type": {"description": "0 team (only for phase2) 1 個人戦", "type": "integer"}, "duration": {"type": "object", "properties": {"from": {"description": "開催日 >= From", "type": "string"}, "to": {"description": "開催日 <= To", "type": "string"}}}, "hidden_hole_setted": {"description": "隠しホール設定済み", "type": "boolean"}, "is_front_system": {"description": "is フロントシステム", "type": "boolean"}, "joined_players_count": {"description": "参加人数", "type": "integer"}, "participation_fee": {"description": "参加料金 円", "type": "integer"}, "prize_condition_setted": {"description": "入賞条件設定済み", "type": "boolean"}, "shared_key": {"description": "共有キー", "type": "string"}}}, "compe.OnlineCompeCreationReq": {"type": "object", "required": ["basic", "compe_setting", "compe_type_setting", "other_setting"], "properties": {"basic": {"$ref": "#/definitions/compe.Basic"}, "compe_setting": {"$ref": "#/definitions/compe.CompeSetting"}, "compe_type_setting": {"$ref": "#/definitions/compe.CompeTypeSetting"}, "other_setting": {"$ref": "#/definitions/compe.OtherSetting"}, "private_setting": {"$ref": "#/definitions/compe.PrivateSetting"}}}, "compe.OnlineCompeDefaultSettingReq": {"type": "object", "properties": {"compe_setting": {"description": "コンペ設定", "allOf": [{"$ref": "#/definitions/compe.DefaultCompeSetting"}]}, "handicap": {"description": "ハンディキャップSetting", "allOf": [{"$ref": "#/definitions/compe.DefaultHandicap"}]}, "other_setting": {"description": "その他設定", "allOf": [{"$ref": "#/definitions/compe.DefaultOtherSetting"}]}}}, "compe.OnlineCompeDefaultSettingResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー 500 内部エラー", "type": "integer"}, "data": {"type": "object", "properties": {"compe_setting": {"$ref": "#/definitions/compe.DefaultCompeSetting"}, "handicap": {"$ref": "#/definitions/compe.DefaultHandicap"}, "other_setting": {"$ref": "#/definitions/compe.DefaultOtherSetting"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "compe.OnlineCompeJoinedPlayersReq": {"type": "object", "required": ["joined_players"], "properties": {"joined_players": {"type": "array", "minItems": 0, "items": {"$ref": "#/definitions/player.JoinedPlayer"}}}}, "compe.OnlineCompeLatestNoResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー 500 内部エラー", "type": "integer"}, "data": {"type": "object", "properties": {"compe_no": {"description": "コンペNo", "type": "integer"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "compe.OnlineCompeOfficeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"compes": {"type": "array", "items": {"$ref": "#/definitions/compe.MergedCompe"}}}}, "message": {"type": "string"}}}, "compe.OnlineCompePlayerJoinReq": {"type": "object", "required": ["player_no"], "properties": {"birthday": {"type": "string"}, "cart_no": {"type": "integer"}, "compe_no": {"type": "integer"}, "course_index": {"type": "integer"}, "gender": {"description": "１：男性、２：女性", "type": "integer"}, "glid_no": {"type": "string"}, "hdcp": {"type": "string"}, "hdcp_index": {"type": "string"}, "is_paid": {"type": "boolean"}, "office_key": {"type": "string"}, "play_date": {"type": "string"}, "player_name": {"type": "string"}, "player_no": {"type": "integer", "minimum": 1}, "team_class_type": {"type": "integer"}, "tee_id": {"type": "string"}}}, "compe.OnlineCompeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"basic": {"$ref": "#/definitions/compe.Basic"}, "compe_setting": {"$ref": "#/definitions/compe.CompeSetting"}, "compe_type_setting": {"$ref": "#/definitions/compe.CompeTypeSetting"}, "other_setting": {"$ref": "#/definitions/compe.OtherSetting"}, "private_setting": {"$ref": "#/definitions/compe.PrivateSetting"}}}, "message": {"type": "string"}}}, "compe.OnlineCompeUpdateReq": {"type": "object", "properties": {"basic": {"$ref": "#/definitions/compe.Basic"}, "compe_setting": {"$ref": "#/definitions/compe.CompeSetting"}, "compe_type_setting": {"$ref": "#/definitions/compe.CompeTypeSetting"}, "other_setting": {"$ref": "#/definitions/compe.OtherSetting"}, "private_setting": {"$ref": "#/definitions/compe.PrivateSetting"}}}, "compe.OtherSetting": {"type": "object", "required": ["leadboard_change", "marker_setting"], "properties": {"leadboard_change": {"description": "リーダーボード切替", "type": "object", "required": ["default", "type"], "properties": {"default": {"description": "option : net, gross when 1  ; \"\" when  type is 0", "type": "string"}, "type": {"description": "0 しない 1 自由設定", "type": "integer"}}}, "marker_setting": {"description": "マーカー設定 0 しない 1 マーカーあり", "type": "integer"}}}, "compe.PlayerRankings": {"type": "object", "properties": {"course_hdcp": {"type": "string"}, "course_index": {"type": "string"}, "hdcp_index": {"type": "string"}, "hole": {"type": "string"}, "hole_index": {"type": "string"}, "hole_number": {"type": "string"}, "hole_score": {"type": "array", "items": {"$ref": "#/definitions/compe.RankingHoleScore"}}, "input_hole_count": {"type": "integer"}, "is_tied": {"type": "integer"}, "order_net": {"type": "string"}, "par_gross": {"type": "integer"}, "par_net": {"type": "number"}, "player_name": {"type": "string"}, "player_no": {"type": "string"}, "pos": {"type": "string"}, "pos_net": {"type": "string"}, "score_gross": {"type": "integer"}, "score_net": {"type": "number"}}}, "compe.PrivateSetting": {"type": "object", "required": ["course_setting", "hidden_hole"], "properties": {"course_setting": {"description": "コース設定", "type": "object", "additionalProperties": {"type": "string"}}, "hidden_hole": {"description": "隠しホール設定 list", "type": "array", "items": {"$ref": "#/definitions/compe.HiddenHoleSetting"}}}}, "compe.RankingHoleScore": {"type": "object", "properties": {"course_index": {"type": "string"}, "hole_hdcp": {"type": "integer"}, "hole_index": {"type": "string"}, "hole_number": {"type": "string"}, "score": {"type": "string"}, "stroke": {"type": "string"}}}, "compe.ScoreCourses": {"type": "object", "properties": {"course_index": {"type": "string"}, "course_name": {"type": "string"}, "holes": {"type": "array", "items": {"$ref": "#/definitions/compe.Holes"}}, "start_hole": {"type": "string"}}}, "compe.SearchPlayerInfoReq": {"type": "object", "required": ["search_type"], "properties": {"birthday": {"description": "yyyy-MM-dd", "type": "string"}, "glid_no": {"type": "string"}, "hdcp_date": {"description": "handy.handicap.hdcp_date  yyyy-MM-dd", "type": "string"}, "player_name": {"type": "string"}, "search_type": {"description": "serach_type 1：internal api 2: external api 3: internal api + external api", "type": "integer"}}}, "compe.UploadImgResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"url": {"type": "string"}}}, "message": {"type": "string"}}}, "course.Course": {"type": "object", "properties": {"course_index": {"type": "string"}, "course_name": {"type": "string"}, "holes": {"type": "array", "items": {"$ref": "#/definitions/course.Hole"}}, "start_hole": {"type": "string"}}}, "course.CoursesResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/course.Course"}}, "msg": {"type": "string"}}}, "course.Hole": {"type": "object", "properties": {"green_index": {"type": "string"}, "hole_index": {"type": "string"}, "used_hdcp": {"type": "string"}, "used_par": {"type": "string"}}}, "mi-restful-api_response_player.PlayerCompe": {"type": "object", "properties": {"compe_basic": {"$ref": "#/definitions/compe.Basic"}, "compe_name": {"type": "string"}, "compe_no": {"type": "integer"}, "compe_setting": {"$ref": "#/definitions/compe.CompeSetting"}, "compe_type_setting": {"$ref": "#/definitions/compe.CompeTypeSetting"}, "other_setting": {"$ref": "#/definitions/compe.OtherSetting"}, "player": {"$ref": "#/definitions/player.JoinedPlayer"}}}, "player.JoinedPlayer": {"type": "object", "required": ["cart_no", "player_no"], "properties": {"birthday": {"description": "eg 1942/10/13", "type": "string"}, "cart_no": {"type": "integer", "minimum": 1}, "course_index": {"type": "integer"}, "gender": {"type": "integer"}, "glid_no": {"type": "string"}, "hdcp": {"type": "string"}, "hdcp_index": {"type": "string"}, "is_paid": {"type": "boolean"}, "office_key": {"type": "string"}, "play_date": {"type": "string"}, "player_name": {"type": "string"}, "player_no": {"type": "integer", "minimum": 1}, "playing_hdcp": {"type": "string"}, "team_class_type": {"type": "integer"}, "tee_id": {"type": "string"}}}, "player.JoinedPlayesResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/player.JoinedPlayer"}}, "msg": {"type": "string"}}}, "player.PlayerCompeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"description": "kv , key is compe no value is joined player", "type": "array", "items": {"$ref": "#/definitions/mi-restful-api_response_player.PlayerCompe"}}, "msg": {"type": "string"}}}, "player.PlayerInfo": {"type": "object", "properties": {"birthday": {"type": "string"}, "gender": {"type": "integer"}, "glid_no": {"type": "string"}, "hdcp_index": {"type": "string"}, "home_club_name": {"type": "string"}, "player_name": {"type": "string"}}}, "player.PlayerInfoResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/player.PlayerInfo"}}, "msg": {"type": "string"}}}, "request.EvaluationPutReq": {"type": "object", "required": ["content", "score", "stage"], "properties": {"content": {"description": "content 評価文言", "type": "string", "maxLength": 200}, "id": {"description": "id 評価段階", "type": "integer"}, "score": {"description": "score 評価配点", "type": "integer"}, "stage": {"description": "stage だんかい", "type": "integer"}}}, "request.OfficeSettingsCreateReq": {"type": "object", "required": ["card_reader_type", "enable_questionnaire", "enable_self_score_print", "enable_start_guide"], "properties": {"card_reader_type": {"description": "カードリード種類　0:なし　1:バーコードリード　2:ICカードリード", "type": "integer", "maximum": 2, "minimum": 0}, "enable_questionnaire": {"description": "アンケート機能　0:無効　1:有効", "type": "integer", "maximum": 1, "minimum": 0}, "enable_self_score_print": {"description": "セルフスコア印刷機能　0:無効　1:有効", "type": "integer", "maximum": 1, "minimum": 0}, "enable_start_guide": {"description": "スタート案内機能　0:無効　1:有効", "type": "integer", "maximum": 1, "minimum": 0}}}, "request.QuesnaireSettingsCreateReq": {"type": "object", "required": ["caddy_name_type"], "properties": {"caddy_name_type": {"description": "キャディの名前種類：1(キャディ)、2(コースアテンダント)", "type": "integer", "maximum": 2, "minimum": 1}}}, "request.Question": {"type": "object", "required": ["content", "require", "type"], "properties": {"content": {"description": "content 設問内容", "type": "string", "maxLength": 200}, "require": {"description": "require 必須", "type": "integer"}, "type": {"description": "type 設問タイプ", "type": "integer"}}}, "request.QuestionnaireCreateReq": {"type": "object", "required": ["cart_no", "played_date", "player_id", "player_name", "survey"], "properties": {"caddy_id": {"description": "caddy_id キャディID", "type": "string", "maxLength": 200}, "caddy_name": {"description": "caddy_id キャディ名前", "type": "string", "maxLength": 200}, "cart_no": {"description": "cart_no カート番号", "type": "string"}, "feedback": {"description": "feedback Custom Comment", "type": "string", "maxLength": 1100}, "feedback_caddy": {"description": "キャディのfeedback Custom Comment", "type": "string", "maxLength": 1100}, "feedback_golf": {"description": "ゴルフ場のfeedback Custom Comment", "type": "string", "maxLength": 1100}, "played_date": {"description": "played_date プレイ時間", "type": "string", "maxLength": 200}, "player_id": {"description": "player_id プレイヤーid", "type": "string", "maxLength": 200}, "player_name": {"description": "player_name プレイヤー名", "type": "string", "maxLength": 200}, "serial": {"description": "serial from operation-info (combine date and players sorted ids)", "type": "string"}, "sort_key": {"description": "sort_key dynamodb sort_key", "type": "string"}, "start_course": {"description": "start_course スタートコース", "type": "string", "maxLength": 200}, "start_time": {"description": "start_time スタート時間", "type": "string", "maxLength": 200}, "survey": {"description": "survey 設問", "type": "array", "items": {"$ref": "#/definitions/request.Survey"}}}}, "request.QuestionnaireIndexReq": {"type": "object", "required": ["end_date", "start_date", "weekday"], "properties": {"caddy_id": {"description": "caddy_id キャディID", "type": "integer"}, "end_date": {"description": "end_date 終了時間", "type": "string"}, "limit": {"description": "limit １ページ当たり件数", "type": "integer"}, "page": {"description": "page ページ数 default 1 start", "type": "integer"}, "start_date": {"description": "start_date 開始時間", "type": "string"}, "weekday": {"description": "weekday 曜日  1 平日、2 週末、 3 指定なし", "type": "integer", "maximum": 3, "minimum": 0}}}, "request.StartGuideSettingsCreateReq": {"type": "object", "required": ["autostart_type", "enable_autostart", "enable_start_time", "main_text_always", "start_number", "start_time_schedule", "sub_text_always", "sub_text_auto"], "properties": {"autostart_type": {"description": "自動スタート案内基準", "type": "integer", "maximum": 1, "minimum": 0}, "enable_autostart": {"description": "自動スタート案内表示", "type": "integer", "maximum": 1, "minimum": 0}, "enable_start_time": {"description": "スタート予定時間表示", "type": "integer", "maximum": 1, "minimum": 0}, "main_text_always": {"description": "メイン文言（常時表示）", "type": "string", "maxLength": 30}, "start_number": {"description": "スタートまでの順番", "type": "integer"}, "start_time_schedule": {"description": "スタート予定時間", "type": "integer"}, "sub_text_always": {"description": "サブ文言（常時表示）", "type": "string", "maxLength": 45}, "sub_text_auto": {"description": "サブ文言（自動案内）", "type": "string", "maxLength": 45}}}, "request.Survey": {"type": "object", "required": ["answer", "id"], "properties": {"answer": {"description": "answer 回答id", "type": "integer"}, "id": {"description": "id question_id", "type": "integer"}}}, "request.UpdateIndexParams": {"type": "object", "required": ["id", "index"], "properties": {"id": {"description": "id 設問ID", "type": "integer"}, "index": {"description": "index 設問順番", "type": "integer"}}}, "response.Answer": {"type": "object", "properties": {"answer": {"description": "answer 回答", "type": "integer"}, "id": {"description": "id 設問ID", "type": "integer"}}}, "response.AnswerSurvey": {"type": "object", "properties": {"answers": {"description": "answers  評価", "type": "array", "items": {"$ref": "#/definitions/response.Evaluation"}}, "id": {"description": "id 設問ID", "type": "integer"}}}, "response.BarcodeToLockerResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "object", "properties": {"locker_no": {"description": "変換後のロッカーキー", "type": "string"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.CaddyAnalysisData": {"type": "object", "properties": {"month": {"description": "month 年月", "type": "string"}, "survey": {"description": "survey キャディ", "type": "array", "items": {"$ref": "#/definitions/response.CaddySurvey"}}}}, "response.CaddyAnalysisResp": {"type": "object", "properties": {"code": {"description": "code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "array", "items": {"$ref": "#/definitions/response.CaddyAnalysisData"}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.CaddyData": {"type": "object", "properties": {"caddy_name": {"description": "caddy_name キャディ名", "type": "string"}, "caddy_no": {"description": "caddy_no キャディ番号", "type": "string"}, "id": {"description": "id ID", "type": "integer"}}}, "response.CaddyIndexResp": {"type": "object", "properties": {"code": {"description": "code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "array", "items": {"$ref": "#/definitions/response.CaddyData"}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.CaddyOnResp": {"type": "object", "properties": {"caddyon": {"description": "caddyon データ", "type": "string"}, "code": {"description": "code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "message": {"description": "message メッセージ", "type": "string"}, "printon": {"description": "printon データ", "type": "string"}}}, "response.CaddySurvey": {"type": "object", "properties": {"caddy_id": {"description": "caddy_id キャディID", "type": "string"}, "survey": {"description": "survey 設問", "type": "array", "items": {"$ref": "#/definitions/response.AnswerSurvey"}}}}, "response.CartInfoData": {"type": "object", "properties": {"caddy_id": {"description": "caddy_id キャディID", "type": "string"}, "caddy_name": {"description": "caddy_id キャディName", "type": "string"}, "cart_no": {"description": "cart_no カート番号", "type": "string"}, "exist_score": {"description": "exist_score exist score", "type": "boolean"}, "played_date": {"description": "played_date プレイ時間", "type": "string"}, "player": {"description": "player プレイヤー", "type": "array", "items": {"$ref": "#/definitions/response.CartPlayer"}}, "serial": {"description": "serial for version 2", "type": "string"}, "sort_key": {"description": "sort_key sk", "type": "string"}, "start_course": {"description": "start_course スタートコース", "type": "string"}, "start_time": {"description": "caddy_name スタート時間", "type": "string"}}}, "response.CartInfoResp": {"type": "object", "properties": {"code": {"description": "code 简码 正常 0 ， 401 未授权， 500 内部错误", "type": "integer"}, "data": {"description": "data 数据", "type": "array", "items": {"$ref": "#/definitions/response.CartInfoData"}}, "message": {"description": "message 简述", "type": "string"}}}, "response.CartPlayer": {"type": "object", "properties": {"club_checked": {"description": "club_checked クラブ確認済み", "type": "boolean"}, "id": {"description": "id プレイヤーID", "type": "string"}, "name": {"description": "name プレイヤー名", "type": "string"}}}, "response.Evaluation": {"type": "object", "properties": {"count": {"description": "count 回答数", "type": "integer"}, "id": {"description": "id 評価ID", "type": "integer"}}}, "response.EvaluationIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "array", "items": {"$ref": "#/definitions/response.EvaluationRespData"}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.EvaluationRespData": {"type": "object", "properties": {"content": {"description": "content 評価文言", "type": "string"}, "id": {"description": "id 評価段階", "type": "integer"}, "score": {"description": "score 評価配点", "type": "integer"}, "stage": {"description": "stage だんかい", "type": "integer"}}}, "response.EvaluationUpdateResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "array", "items": {"$ref": "#/definitions/response.EvaluationRespData"}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.OfficeSettingsCreateResp": {"type": "object", "properties": {"code": {"description": "code 简码 正常 0 ， 401 未授权， 500 内部错误", "type": "integer"}, "message": {"description": "message 简述", "type": "string"}}}, "response.OfficeSettingsIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "object", "properties": {"card_reader_type": {"description": "カードリード種類　0:なし　1:バーコードリード　2:ICカードリード", "type": "integer"}, "enable_questionnaire": {"description": "アンケート機能　0:無効　1:有効", "type": "integer"}, "enable_self_score_print": {"description": "セルフスコア印刷機能　0:無効　1:有効", "type": "integer"}, "enable_start_guide": {"description": "スタート案内機能　0:無効　1:有効", "type": "integer"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.Ping": {"type": "object", "properties": {"aws_config": {"type": "boolean"}, "git_version": {"type": "string"}, "message": {"type": "string"}}}, "response.QuesnaireSettingsCreateResp": {"type": "object", "properties": {"code": {"description": "code 简码 正常 0 ， 401 未授权， 500 内部错误", "type": "integer"}, "message": {"description": "message 简述", "type": "string"}}}, "response.QuesnaireSettingsIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "object", "properties": {"caddy_name_type": {"description": "キャディの名前種類：1(キャディ)、2(コースアテンダント)", "type": "integer"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.QuestionCreateResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー 500 内部エラー", "type": "integer"}, "id": {"description": "id リソースid", "type": "integer"}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.QuestionDelResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー， 500 内部エラー", "type": "integer"}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.QuestionIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data 数据", "type": "array", "items": {"$ref": "#/definitions/response.QuestionRespData"}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.QuestionRespData": {"type": "object", "properties": {"content": {"description": "content 設問内容", "type": "string"}, "id": {"description": "id 設問ID", "type": "integer"}, "index": {"description": "index 設問順番", "type": "integer"}, "require": {"description": "require 必須", "type": "integer"}, "type": {"description": "type 設問タイプ", "type": "integer"}}}, "response.QuestionShowResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "allOf": [{"$ref": "#/definitions/response.QuestionRespData"}]}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.QuestionnaireCreateResp": {"type": "object", "properties": {"code": {"description": "code 简码 正常 0 ， 401 未授权， 500 内部错误", "type": "integer"}, "id": {"description": "id 创建资源的id", "type": "integer"}, "message": {"description": "message 简述", "type": "string"}}}, "response.QuestionnaireExportCsv": {"type": "object", "properties": {"code": {"description": "code 简码 正常 0 ， 401 未授权， 500 内部错误", "type": "integer"}, "message": {"description": "message 简述", "type": "string"}}}, "response.QuestionnaireIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "current_page": {"description": "current_page 現在ページ", "type": "integer"}, "data": {"description": "data データ", "type": "array", "items": {"$ref": "#/definitions/response.QuestionnaireRespData"}}, "from": {"description": "from 開始件数", "type": "integer"}, "last_page": {"description": "last_page 全ページ数", "type": "integer"}, "message": {"description": "message メッセージ", "type": "string"}, "per_page": {"description": "per_page １ページ当たり件数", "type": "integer"}, "to": {"description": "to 終了件数", "type": "integer"}, "total": {"description": "total 条件を満たす件数", "type": "integer"}}}, "response.QuestionnaireRespData": {"type": "object", "properties": {"caddy_id": {"description": "caddy_id キャディID", "type": "string"}, "cart_no": {"description": "cart_no カート番号", "type": "string"}, "id": {"description": "id 主キーID", "type": "integer"}, "played_date": {"description": "played_date プレイ時間", "type": "string"}, "start_course": {"description": "start_course スタートコース", "type": "string"}, "start_time": {"description": "start_Time スタート時間", "type": "string"}, "survey": {"description": "survey 設問", "type": "array", "items": {"$ref": "#/definitions/response.Survey"}}}}, "response.StartGuideInfoIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "object", "properties": {"main_text_always": {"description": "メイン文言（常時表示）", "type": "string"}, "main_text_auto": {"description": "メイン文言（自動案内）", "type": "string"}, "sub_text_always": {"description": "サブ文言（常時表示）", "type": "string"}, "sub_text_auto": {"description": "サブ文言（自動案内）", "type": "string"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.StartGuideSettingsCreateResp": {"type": "object", "properties": {"code": {"description": "code 简码 正常 0 ， 401 未授权， 500 内部错误", "type": "integer"}, "message": {"description": "message 简述", "type": "string"}}}, "response.StartGuideSettingsIndexResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "object", "properties": {"autostart_type": {"description": "自動スタート案内基準", "type": "integer"}, "enable_autostart": {"description": "自動スタート案内表示", "type": "integer"}, "enable_start_time": {"description": "スタート予定時間表示", "type": "integer"}, "main_text_always": {"description": "メイン文言（常時表示）", "type": "string"}, "start_number": {"description": "スタートまでの順番", "type": "integer"}, "start_time_schedule": {"description": "スタート予定時間", "type": "integer"}, "sub_text_always": {"description": "サブ文言（常時表示）", "type": "string"}, "sub_text_auto": {"description": "サブ文言（自動案内）", "type": "string"}}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.StatisticalData": {"type": "object", "properties": {"month": {"description": "month 年月", "type": "string"}, "survey": {"description": "survey キャディ", "type": "array", "items": {"$ref": "#/definitions/response.AnswerSurvey"}}}}, "response.StatisticalResp": {"type": "object", "properties": {"code": {"description": "code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー", "type": "integer"}, "data": {"description": "data データ", "type": "array", "items": {"$ref": "#/definitions/response.StatisticalData"}}, "message": {"description": "message メッセージ", "type": "string"}}}, "response.Survey": {"type": "object", "properties": {"answer_time": {"description": "answer_time 回答時間", "type": "string"}, "answers": {"description": "answers 回答", "type": "array", "items": {"$ref": "#/definitions/response.Answer"}}, "feedback": {"description": "feedback custom comment", "type": "string"}, "feedback_caddy": {"description": "キャディのfeedback custom comment", "type": "string"}, "feedback_golf": {"description": "ゴルフ場のfeedback custom comment", "type": "string"}, "id": {"description": "id プレイヤーID", "type": "string"}, "name": {"description": "name プレイヤー名", "type": "string"}}}, "startguidance.CartInfoReq": {"type": "object", "required": ["locker_no"], "properties": {"locker_no": {"description": "LockerNo", "type": "string", "minLength": 0}}}, "startguidance.CartInfoResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/startguidance.LockerCartInfo"}, "message": {"type": "string"}}}, "startguidance.LockerCartInfo": {"type": "object", "properties": {"cart_no": {"type": "string"}, "current_player": {"$ref": "#/definitions/startguidance.Player"}, "other_players": {"type": "array", "items": {"$ref": "#/definitions/startguidance.Player"}}}}, "startguidance.OtherPlayer": {"type": "object", "properties": {"checked_in": {"type": "boolean"}, "locker_no": {"type": "string"}, "player_name": {"type": "string"}, "player_no": {"type": "string"}}}, "startguidance.Player": {"type": "object", "properties": {"locker_no": {"type": "string"}, "player_name": {"type": "string"}, "player_no": {"type": "string"}, "scheduled_start_course_index": {"type": "string"}, "scheduled_start_course_name": {"type": "string"}, "scheduled_start_time": {"type": "string"}, "score_sort_key": {"type": "string"}, "start_time": {"type": "string"}}}, "startguidance.PlayerTeeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/startguidance.PlayerTeeTime"}, "msg": {"type": "string"}}}, "startguidance.PlayerTeeTime": {"type": "object", "properties": {"cart_no": {"type": "string"}, "course_index": {"type": "string"}, "course_name": {"type": "string"}, "locker_no": {"type": "string"}, "other_players": {"type": "array", "items": {"$ref": "#/definitions/startguidance.OtherPlayer"}}, "player_name": {"type": "string"}, "status": {"type": "string"}, "time": {"type": "string"}}}, "startguidance.TeeCourseData": {"type": "object", "properties": {"course_index": {"type": "string"}, "course_name": {"type": "string"}, "tee_times": {"type": "array", "items": {"$ref": "#/definitions/startguidance.TeeTime"}}}}, "startguidance.TeeTime": {"type": "object", "properties": {"cart_no": {"type": "string"}, "no": {"type": "string"}, "players": {"type": "array", "items": {"$ref": "#/definitions/startguidance.Player"}}, "status": {"type": "string"}, "time": {"type": "string"}}}, "startguidance.TeeTimePersonalReq": {"type": "object", "required": ["locker_no"], "properties": {"locker_no": {"description": "LockerNo", "type": "string", "minLength": 0}}}, "startguidance.TeeTimeReq": {"type": "object", "required": ["from", "limit"], "properties": {"from": {"description": "from HH:MM", "type": "string"}, "limit": {"description": "limit >0", "type": "integer", "minimum": 0}}}, "startguidance.TeeTimeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/startguidance.TeeTimesData"}, "msg": {"type": "string"}}}, "startguidance.TeeTimesData": {"type": "object", "properties": {"current_time": {"type": "string"}, "information": {"type": "string"}, "tee_course": {"type": "array", "items": {"$ref": "#/definitions/startguidance.TeeCourseData"}}}}, "tee.DelegateCompe": {"type": "object", "properties": {"compe_name": {"type": "string"}, "compe_no": {"type": "integer"}, "is_front_system": {"type": "boolean"}}}, "tee.TeeCartData": {"type": "object", "properties": {"cart_no": {"type": "integer"}, "delegate_compe": {"$ref": "#/definitions/tee.DelegateCompe"}, "players": {"type": "array", "items": {"$ref": "#/definitions/tee.TeePlayerData"}}, "scheduled_start_time": {"type": "string"}, "start_time": {"type": "string"}}}, "tee.TeeCourseData": {"type": "object", "properties": {"cart_data": {"type": "array", "items": {"$ref": "#/definitions/tee.TeeCartData"}}, "course_index": {"type": "string"}, "course_name": {"type": "string"}}}, "tee.TeeInfo": {"type": "object", "properties": {"men_tee_name": {"type": "string"}, "tee_id": {"type": "string"}, "women_tee_name": {"type": "string"}}}, "tee.TeeInfoResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/tee.TeeInfo"}}, "msg": {"type": "string"}}}, "tee.TeePlayer": {"type": "object", "properties": {"birthday": {"description": "eg 1942/10/13", "type": "string"}, "cart_no": {"type": "integer"}, "gender": {"description": "１：男性、２：女性", "type": "integer"}, "glid_no": {"type": "string"}, "hdcp": {"type": "string"}, "hdcp_index": {"type": "string"}, "locker_no": {"type": "string"}, "office_key": {"type": "string"}, "old_compe_no": {"type": "string"}, "play_date": {"type": "string"}, "player_name": {"type": "string"}, "player_no": {"type": "integer"}, "private_hdcp": {"type": "string"}, "score_hash": {"type": "string"}, "tee_id": {"type": "string"}, "whs_hdcp": {"type": "string"}}}, "tee.TeePlayerData": {"type": "object", "properties": {"birthday": {"type": "string"}, "gender": {"type": "integer"}, "glid_no": {"type": "string"}, "hdcp": {"type": "string"}, "hdcp_index": {"type": "string"}, "joined_compes": {"type": "array", "items": {"type": "object", "properties": {"compe_no": {"type": "integer"}}}}, "locker_no": {"type": "integer"}, "player_name": {"type": "string"}, "player_no": {"type": "integer"}, "tee_id": {"type": "string"}}}, "tee.TeePlayerResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/tee.TeePlayer"}}, "msg": {"type": "string"}}}, "tee.TeeSheetPlayerUpdateReq": {"type": "object", "required": ["cart_no", "play_date", "player_no"], "properties": {"birthday": {"type": "string"}, "cart_no": {"type": "integer", "minimum": 1}, "gender": {"type": "integer"}, "glid_no": {"type": "string"}, "hdcp": {"type": "string"}, "hdcp_index": {"type": "string"}, "play_date": {"type": "string"}, "player_no": {"type": "integer", "minimum": 1}, "scheduled_start_time": {"description": "should be \"hh:mm\" or null", "type": "string"}}}, "tee.TeeSheetResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/tee.TeeCourseData"}}, "msg": {"type": "string"}}}}}