package repocompe_test

import (
	"errors"
	"fmt"
	"mi-restful-api/model/compe"
	"mi-restful-api/model/player"
	req "mi-restful-api/request/compe"
	"mi-restful-api/testutils/mocks"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// MockDynamoClient for mocking DynamoDB operations
type MockDynamoClient struct {
	mock.Mock
}

func (m *MockDynamoClient) PutItem(tableName string, item map[string]types.AttributeValue) (*dynamodb.PutItemOutput, error) {
	args := m.Called(tableName, item)
	return args.Get(0).(*dynamodb.PutItemOutput), args.Error(1)
}

func (m *MockDynamoClient) UpdateItem(input *dynamodb.UpdateItemInput) (*dynamodb.UpdateItemOutput, error) {
	args := m.Called(input)
	return args.Get(0).(*dynamodb.UpdateItemOutput), args.Error(1)
}

func (m *MockDynamoClient) Query(input *dynamodb.QueryInput) (*dynamodb.QueryOutput, error) {
	args := m.Called(input)
	return args.Get(0).(*dynamodb.QueryOutput), args.Error(1)
}

func (m *MockDynamoClient) GetItem(tableName string, key map[string]types.AttributeValue) (*dynamodb.GetItemOutput, error) {
	args := m.Called(tableName, key)
	return args.Get(0).(*dynamodb.GetItemOutput), args.Error(1)
}

func setupMockDB(t *testing.T) (sqlmock.Sqlmock, *gorm.DB) {
	db, mock, err := mocks.GetMockDB()
	if err != nil {
		t.Fatalf("Failed to open mock sql db, err: %v", err)
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	assert.NoError(t, err)

	return mock, gormDB
}

func TestCreateOnlineCompe(t *testing.T) {
	// Create test data
	onlineCompe := compe.OnlineCompe{
		PartitionKey: "#online-compe-basic",
		SortKey:      "online-compe_1",
		Details: struct {
			Basic            compe.Basic            `json:"basic" dynamodbav:"basic"`
			CompeSetting     compe.CompeSetting     `json:"compe_setting" dynamodbav:"compe_setting"`
			CompeTypeSetting compe.CompeTypeSetting `json:"compe_type_setting" dynamodbav:"compe_type_setting"`
			OtherSetting     compe.OtherSetting     `json:"other_setting" dynamodbav:"other_setting"`
		}{
			Basic: compe.Basic{
				CompeNo:   1,
				CompeName: "Test Competition",
				Organizer: "Test Organizer",
			},
		},
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the structure can be marshaled properly (without calling actual function)
	_, err := attributevalue.MarshalMap(onlineCompe)
	assert.NoError(t, err)

	// Test that required fields are set
	assert.Equal(t, "#online-compe-basic", onlineCompe.PartitionKey)
	assert.Equal(t, "online-compe_1", onlineCompe.SortKey)
	assert.Equal(t, 1, onlineCompe.Details.Basic.CompeNo)
	assert.Equal(t, "Test Competition", onlineCompe.Details.Basic.CompeName)
}

func TestUpdateOnlineCompe(t *testing.T) {
	// Create test data
	onlineCompe := compe.OnlineCompe{
		PartitionKey: "#online-compe-basic",
		SortKey:      "online-compe_1",
		Details: struct {
			Basic            compe.Basic            `json:"basic" dynamodbav:"basic"`
			CompeSetting     compe.CompeSetting     `json:"compe_setting" dynamodbav:"compe_setting"`
			CompeTypeSetting compe.CompeTypeSetting `json:"compe_type_setting" dynamodbav:"compe_type_setting"`
			OtherSetting     compe.OtherSetting     `json:"other_setting" dynamodbav:"other_setting"`
		}{
			Basic: compe.Basic{
				CompeNo:   1,
				CompeName: "Updated Test Competition",
			},
		},
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the Details can be marshaled properly
	_, err := attributevalue.MarshalMap(onlineCompe.Details)
	assert.NoError(t, err)

	// Test that the update data is valid
	assert.Equal(t, "Updated Test Competition", onlineCompe.Details.Basic.CompeName)
	assert.Equal(t, 1, onlineCompe.Details.Basic.CompeNo)
}

func TestDeleteOnlineCompeByNo(t *testing.T) {
	// Test that the function signature and basic logic work
	// Since the actual function just returns true, nil, we test the concept
	compeNo := 1
	assert.Greater(t, compeNo, 0, "Competition number should be positive")
}

func TestOnlineCompeByNo(t *testing.T) {
	// Test DynamoDB attribute value creation and unmarshaling
	mockItem := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: "#online-compe-basic"},
		"sort_key":      &types.AttributeValueMemberS{Value: "online-compe_1"},
		"details": &types.AttributeValueMemberM{
			Value: map[string]types.AttributeValue{
				"basic": &types.AttributeValueMemberM{
					Value: map[string]types.AttributeValue{
						"compe_no":   &types.AttributeValueMemberN{Value: "1"},
						"compe_name": &types.AttributeValueMemberS{Value: "Test Competition"},
					},
				},
			},
		},
		"expiration_time": &types.AttributeValueMemberN{Value: "1234567890"},
		"updated_at":      &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
	}

	// Test that we can unmarshal the mock data
	var result compe.OnlineCompe
	err := attributevalue.UnmarshalMap(mockItem, &result)
	assert.NoError(t, err)
	assert.Equal(t, "#online-compe-basic", result.PartitionKey)
	assert.Equal(t, "online-compe_1", result.SortKey)
}

func TestOnlineCompeByNoNotFound(t *testing.T) {
	// Test empty result handling
	emptyItems := []map[string]types.AttributeValue{}
	assert.Equal(t, 0, len(emptyItems), "Empty items should have length 0")

	// Test error creation
	err := errors.New("no data")
	assert.Error(t, err)
	assert.Equal(t, "no data", err.Error())
}

func TestOnlineCompeLastNo(t *testing.T) {
	// Test OnlineCompeIndex structure
	index := compe.OnlineCompeIndex{
		ID:               1,
		OfficeKey:        "test_office",
		CompeID:          5,
		CompeName:        "Latest Competition",
		CompeType:        1,
		AggregationTypes: "handy",
		TimeZone:         "Asia/Tokyo",
		StartedAt:        time.Now(),
		EndedAt:          time.Now().Add(24 * time.Hour),
		CreatedAt:        time.Now(),
	}

	// Test that the structure is valid
	assert.Equal(t, 5, index.CompeID)
	assert.Equal(t, "test_office", index.OfficeKey)
	assert.Equal(t, "Latest Competition", index.CompeName)
	assert.Equal(t, "handy", index.AggregationTypes)
}

func TestOnlineCompeLastNoEmpty(t *testing.T) {
	// Test empty slice handling
	var indices []compe.OnlineCompeIndex
	assert.Equal(t, 0, len(indices), "Empty slice should have length 0")

	// Test default value when no competition found
	defaultCompeNo := 0
	assert.Equal(t, 0, defaultCompeNo, "Default competition number should be 0")
}

func TestUpdateDefaultOnlineCompeSetting(t *testing.T) {
	// Create test data
	defaultSetting := compe.OnlineCompeDefaultSetting{
		PartitionKey: "#online-compe-default",
		SortKey:      "setting_test_office",
		Details: struct {
			CompeSetting compe.DefaultCompeSetting `json:"compe_setting" dynamodbav:"compe_setting"`
			OtherSetting compe.DefaultOtherSetting `json:"other_setting" dynamodbav:"other_setting"`
			Handicap     compe.DefaultHandicap     `json:"handicap" dynamodbav:"handicap"`
		}{
			CompeSetting: compe.DefaultCompeSetting{
				EntryFromNavi:      1,
				RankingAggregation: 0,
				Round:              "1",
			},
		},
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the Details can be marshaled properly
	_, err := attributevalue.MarshalMap(defaultSetting.Details)
	assert.NoError(t, err)

	// Test that the structure is valid
	assert.Equal(t, "#online-compe-default", defaultSetting.PartitionKey)
	assert.Equal(t, "setting_test_office", defaultSetting.SortKey)
	assert.Equal(t, 1, defaultSetting.Details.CompeSetting.EntryFromNavi)
}

func TestOnlineCompeDefaultSetting(t *testing.T) {
	// Test DynamoDB attribute value creation and unmarshaling
	mockItem := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: "#online-compe-default"},
		"sort_key":      &types.AttributeValueMemberS{Value: "setting_test_office"},
		"details": &types.AttributeValueMemberM{
			Value: map[string]types.AttributeValue{
				"compe_setting": &types.AttributeValueMemberM{
					Value: map[string]types.AttributeValue{
						"entry_from_navi":     &types.AttributeValueMemberN{Value: "1"},
						"ranking_aggregation": &types.AttributeValueMemberN{Value: "0"},
						"round":               &types.AttributeValueMemberS{Value: "1"},
					},
				},
			},
		},
		"expiration_time": &types.AttributeValueMemberN{Value: "1234567890"},
		"updated_at":      &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
	}

	// Test that we can unmarshal the mock data
	var result compe.OnlineCompeDefaultSetting
	err := attributevalue.UnmarshalMap(mockItem, &result)
	assert.NoError(t, err)
	assert.Equal(t, "#online-compe-default", result.PartitionKey)
	assert.Equal(t, "setting_test_office", result.SortKey)
}

func TestOnlineCompePrivateByNo(t *testing.T) {
	// Test PrivateSetting structure
	privateSetting := compe.PrivateSetting{
		CourseSetting: map[string]string{
			"course1": "Course 1",
			"course2": "Course 2",
		},
		HiddenHole: []compe.HiddenHoleSetting{},
	}

	// Test that the structure is valid
	assert.NotNil(t, privateSetting.CourseSetting)
	assert.NotNil(t, privateSetting.HiddenHole)
	assert.Equal(t, "Course 1", privateSetting.CourseSetting["course1"])
	assert.Equal(t, 0, len(privateSetting.HiddenHole))
}

func TestUpsertCompeToPlayer(t *testing.T) {
	// Create test data
	compePlayer := compe.CompePlayer{
		PartitionKey: "compe_1",
		SortKey:      "online-compe_player_test_office_20240101_1_123",
		Details: compe.CompePlayerDetails{
			CompeNo:    1,
			OfficeKey:  "test_office",
			PlayerNo:   123,
			PlayerName: "Test Player",
		},
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the structure can be marshaled properly
	_, err := attributevalue.MarshalMap(compePlayer)
	assert.NoError(t, err)

	// Test that the structure is valid
	assert.Equal(t, "compe_1", compePlayer.PartitionKey)
	assert.Equal(t, 1, compePlayer.Details.CompeNo)
	assert.Equal(t, "test_office", compePlayer.Details.OfficeKey)
	assert.Equal(t, 123, compePlayer.Details.PlayerNo)
}

func TestUpsertPlayerToCompe(t *testing.T) {
	// Create test data
	playerCompe := player.PlayerCompe{
		PartitionKey: "test_office",
		SortKey:      "player_online-compe_123_1_1_20240101",
		Details: player.PlayerCompeDetails{
			PlayerNo:   123,
			PlayerName: "Test Player",
			PlayDate:   "20240101",
			CartNo:     1,
			CompeNo:    1,
		},
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the structure can be marshaled properly
	_, err := attributevalue.MarshalMap(playerCompe)
	assert.NoError(t, err)

	// Test cache key generation logic
	cacheKey := fmt.Sprintf("player_online-compe_%s_%d_%s", playerCompe.PartitionKey, playerCompe.Details.PlayerNo, playerCompe.Details.PlayDate)
	expectedKey := "player_online-compe_test_office_123_20240101"
	assert.Equal(t, expectedKey, cacheKey)

	// Test that the structure is valid
	assert.Equal(t, "test_office", playerCompe.PartitionKey)
	assert.Equal(t, 123, playerCompe.Details.PlayerNo)
	assert.Equal(t, "Test Player", playerCompe.Details.PlayerName)
}

func TestUpsertOfficeToCompe(t *testing.T) {
	// Create test data
	officeCompe := compe.OfficeCompe{
		OfficeKey: "test_office",
		SortKey:   "online-compe_1",
		Details: compe.OfficeCompeDetails{
			CompeNo:              1,
			CompeName:            "Test Office Competition",
			CourseSetting:        map[string]string{"course1": "Course 1"},
			HiddenHole:           []compe.HiddenHoleSetting{},
			PrizeConditionSetted: false,
			SharedKey:            "test_share_key",
		},
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the Details can be marshaled properly
	_, err := attributevalue.MarshalMap(officeCompe.Details)
	assert.NoError(t, err)

	// Test that the structure is valid
	assert.Equal(t, "test_office", officeCompe.OfficeKey)
	assert.Equal(t, "online-compe_1", officeCompe.SortKey)
	assert.Equal(t, 1, officeCompe.Details.CompeNo)
	assert.Equal(t, "Test Office Competition", officeCompe.Details.CompeName)
	assert.Equal(t, "Course 1", officeCompe.Details.CourseSetting["course1"])
}

func TestOldCompeStructure(t *testing.T) {
	// Test that we can create and marshal an OldCompe structure
	oldCompe := compe.OldCompe{
		PartitionKey:   "test_office",
		SortKey:        "old-compe_1_20240101",
		ExpirationTime: time.Now().Add(24 * time.Hour).Unix(),
		UpdatedAt:      time.Now(),
	}

	// Test that the structure can be marshaled properly
	_, err := attributevalue.MarshalMap(oldCompe)
	assert.NoError(t, err)
}

func TestLeaderboardRankingShareKeyCreate(t *testing.T) {
	// Test request structure validation
	shareKeyPtr := "ABC123"
	request := req.LeaderboardRankingShareKeyCreateReq{
		CompeNo:  1,
		ShareKey: &shareKeyPtr,
	}

	// Test that the request structure is valid
	assert.Equal(t, 1, request.CompeNo)
	assert.NotNil(t, request.ShareKey)
	assert.Equal(t, "ABC123", *request.ShareKey)

	// Test Redis key generation logic
	redisKey := fmt.Sprintf("%d:shareKey:%s", request.CompeNo, *request.ShareKey)
	expectedKey := "1:shareKey:ABC123"
	assert.Equal(t, expectedKey, redisKey)

	// Test value generation logic
	value := fmt.Sprintf("%d_%s_%s", request.CompeNo, "test_office", "test_office")
	expectedValue := "1_test_office_test_office"
	assert.Equal(t, expectedValue, value)
}

func TestLeaderboardRankingByCompeNo(t *testing.T) {
	// Test DynamoDB attribute value creation and unmarshaling for ranking
	mockItem := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: "#online-compe-ranking"},
		"sort_key":      &types.AttributeValueMemberS{Value: "online-compe_1_test_office_20240101_handy"},
		"details": &types.AttributeValueMemberM{
			Value: map[string]types.AttributeValue{
				"compe_no":  &types.AttributeValueMemberS{Value: "1"},
				"play_date": &types.AttributeValueMemberS{Value: "20240101"},
				"courses":   &types.AttributeValueMemberL{Value: []types.AttributeValue{}},
				"rankings":  &types.AttributeValueMemberL{Value: []types.AttributeValue{}},
			},
		},
		"expiration_time": &types.AttributeValueMemberN{Value: "1234567890"},
		"updated_at":      &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
	}

	// Test that we can unmarshal the mock data
	var result compe.Ranking
	err := attributevalue.UnmarshalMap(mockItem, &result)
	assert.NoError(t, err)
	assert.Equal(t, "#online-compe-ranking", result.PartitionKey)
	assert.Equal(t, "online-compe_1_test_office_20240101_handy", result.SortKey)

	// Test sort key parsing logic
	sortKeyParts := strings.Split(result.SortKey, "_")
	assert.Equal(t, 6, len(sortKeyParts))
	assert.Equal(t, "online-compe", sortKeyParts[0])
	assert.Equal(t, "1", sortKeyParts[1])
	assert.Equal(t, "test", sortKeyParts[2])
	assert.Equal(t, "office", sortKeyParts[3])
	assert.Equal(t, "20240101", sortKeyParts[4])
	assert.Equal(t, "handy", sortKeyParts[5])
}
