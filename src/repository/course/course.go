package course

import (
	"log/slog"
	"mi-restful-api/model/course"
	"strconv"

	"gorm.io/gorm"
)

type CourseRepos struct {
	ConnMySQLMncdb *gorm.DB
}

func (s *CourseRepos) ListCourse(officeId string) ([]course.Course, error) {
	var data []course.Course

	courseData := []course.CourseSetting{}
	resp := s.ConnMySQLMncdb.Model(&course.CourseSetting{}).
		Where("office_id=? and deleted_at is null", officeId).
		Order("course_index ASC").
		Find(&courseData)
	if resp.Error != nil {
		slog.Error("get course_setting error", "error", resp.Error)
		return []course.Course{}, resp.Error
	}

	for _, d := range courseData {
		var holeData []course.HoleSetting
		var holes []course.Hole

		resp = s.ConnMySQLMncdb.Model(&course.HoleSetting{}).
			Where("office_id=? and course_index=? and deleted_at is null", officeId, d.CourseIndex).
			Order("course_index ASC").
			Find(&holeData)
		if resp.Error != nil {
			continue
		}

		for _, h := range holeData {
			holes = append(holes, course.Hole{
				GreenIndex: strconv.Itoa(h.GreenIndex),
				HoleIndex:  strconv.Itoa(h.HoleIndex),
				UsedHdcp:   strconv.Itoa(h.Hdcp),
				UsedPar:    strconv.Itoa(h.Par),
			})
		}

		data = append(data, course.Course{
			CourseIndex: strconv.Itoa(d.CourseIndex),
			CourseName:  d.CourseName,
			StartHole:   strconv.Itoa(d.StartHoleNo),
			Holes:       holes,
		})
	}

	return data, nil
}

func (s *CourseRepos) ListCoursesWithoutHole(officeId string) ([]course.Course, error) {

	var data []course.Course

	courseData := []course.CourseSetting{}
	resp := s.ConnMySQLMncdb.Model(&course.CourseSetting{}).
		Where("office_id=? and deleted_at is null", officeId).
		Order("course_index ASC").
		Find(&courseData)
	if resp.Error != nil {
		slog.Error("get course_setting error", "err", resp.Error)
		return []course.Course{}, resp.Error
	}

	for _, d := range courseData {
		data = append(data, course.Course{
			CourseIndex: strconv.Itoa(d.CourseIndex),
			CourseName:  d.CourseName,
			StartHole:   strconv.Itoa(d.StartHoleNo),
		})
	}

	return data, nil
}
