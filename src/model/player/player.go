package player

import "time"

type PlayerHdcp struct {
	PartitionKey string `json:"partition_key"`
	SortKey      string `json:"sort_key"`
	Details      struct {
		PlayerNo   int    `json:"player_no"`
		PlayerName string `json:"player_name"`
		HdcpIndex  string `json:"hdcp_index"`
		Hdcp       string `json:"hdcp"`
		ExpiredAt  string `json:"expired_at"`
	} `json:"details"`
	ExpirationTime int64     `json:"expiration_time"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type PlayerCompe struct {
	PartitionKey   string             `json:"partition_key" dynamodbav:"partition_key"` // offce key
	SortKey        string             `json:"sort_key" dynamodbav:"sort_key"`           // player_online-compe_${player_no}_${compe_no}_${cart_no}_${play_date}
	Details        PlayerCompeDetails `json:"details" dynamodbav:"details"`
	ExpirationTime int64              `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time          `json:"updated_at" dynamodbav:"updated_at"`
}

type PlayerCompeDetails struct {
	PlayerNo    int     `json:"player_no" dynamodbav:"player_no"`     // player_no
	PlayerName  string  `json:"player_name" dynamodbav:"player_name"` // player_name
	PlayDate    string  `json:"play_date" dynamodbav:"play_date"`
	CartNo      int     `json:"cart_no" dynamodbav:"cart_no"`
	CompeNo     int     `json:"compe_no" dynamodbav:"compe_no"`
	CourseIndex *int    `json:"course_index" dynamodbav:"course_index"`
	ScoreHash   *string `json:"score_hash" dynamodbav:"score_hash"`
}

type Player struct {
	PartitionKey   string        `json:"partition_key" dynamodbav:"partition_key"` //office key
	SortKey        string        `json:"sort_key" dynamodbav:"sort_key"`           //eg.player_20250303_120
	Details        PlayerDetails `json:"details" dynamodbav:"details"`
	ExpirationTime int64         `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time     `json:"updated_at" dynamodbav:"updated_at"`
}

type PlayerDetails struct {
	PlayDate                  string           `json:"play_date" dynamodbav:"play_date"`
	PlayerNo                  string           `json:"player_no" dynamodbav:"player_no"`
	GlidNo                    string           `json:"glid_no" dynamodbav:"glid_no"`
	UserIdToken               string           `json:"user_id_token" dynamodbav:"user_id_token"`
	PlayerName                string           `json:"player_name" dynamodbav:"player_name"`
	PlayerNameOriginal        string           `json:"player_name_original" dynamodbav:"player_name_original"`
	PlayerSex                 string           `json:"player_sex" dynamodbav:"player_sex"`
	PlayerBirthDay            string           `json:"player_birthday" dynamodbav:"player_birthday"`
	CartNo                    string           `json:"cart_no" dynamodbav:"cart_no"`
	CompeNo                   string           `json:"compe_no" dynamodbav:"compe_no"`
	CaddieNo                  string           `json:"caddie_no" dynamodbav:"caddie_no"`
	PrivateHDCP               string           `json:"private_hdcp" dynamodbav:"private_hdcp"`
	WHSHDCP                   string           `json:"whs_hdcp" dynamodbav:"whs_hdcp"`
	ScheduledStartCourseIndex string           `json:"scheduled_start_course_index" dynamodbav:"scheduled_start_course_index"`
	ScheduledStartTime        string           `json:"scheduled_start_time" dynamodbav:"scheduled_start_time"`
	UseExternalCharacter      string           `json:"use_external_character" dynamodbav:"use_external_character"`
	LockerNo                  string           `json:"locker_no" dynamodbav:"locker_no"`
	GolferAttribute           string           `json:"golfer_attribute" dynamodbav:"golfer_attribute"`
	TeeSelectable             string           `json:"tee_selectable" dynamodbav:"tee_selectable"`
	TechnoIdStatus            string           `json:"techno_id_status" dynamodbav:"techno_id_status"`
	CompeStatus               string           `json:"compe_status" dynamodbav:"compe_status"`
	EnableMarkCommittee       string           `json:"enable_mark_committee" dynamodbav:"enable_mark_committee"`
	ScoreHash                 string           `json:"score_hash" dynamodbav:"score_hash"`
	AppliedScoreCount         string           `json:"applied_score_count" dynamodbav:"applied_score_count"`
	GlidNoAssignmentStatus    string           `json:"glid_no_assignment_status" dynamodbav:"glid_no_assignment_status"`
	UsersMatchingName         string           `json:"users_matching_name" dynamodbav:"users_matching_name"`
	ScoreApplyConfig          ScoreApplyConfig `json:"score_apply_config" dynamodbav:"score_apply_config"`
}

type ScoreApplyConfig struct {
	EnableScoreApply string                    `json:"enable_score_apply" dynamodbav:"enable_score_apply"`
	Defaults         ScoreApplyConfigDefaults  `json:"defaults" dynamodbav:"defaults"`
	Selected         ScoreApplyConfigSelected  `json:"selected" dynamodbav:"selected"`
	Courses          []ScoreApplyConfigCourses `json:"courses" dynamodbav:"courses"`
}

type ScoreApplyConfigDefaults struct {
	ScoreRequest   string `json:"score_request" dynamodbav:"score_request"`
	TeeId          string `json:"tee_id" dynamodbav:"tee_id"`
	MarkerPlayerNo string `json:"marker_player_no" dynamodbav:"marker_player_no"`
}

type ScoreApplyConfigSelected struct {
	ScoreRequest   string `json:"score_request" dynamodbav:"score_request"`
	TeeId          string `json:"tee_id" dynamodbav:"tee_id"`
	MarkerPlayerNo string `json:"marker_player_no" dynamodbav:"marker_player_no"`
}

type ScoreApplyConfigCourses struct {
	CourseIndex  string                   `json:"course_index" dynamodbav:"course_index"`
	GreenIndex   string                   `json:"green_index" dynamodbav:"green_index"`
	ScoreRequest string                   `json:"score_request" dynamodbav:"score_request"`
	TeeId        string                   `json:"tee_id" dynamodbav:"tee_id"`
	Attest       []ScoreApplyConfigAttest `json:"attest" dynamodbav:"attest"`
}

type ScoreApplyConfigAttest struct {
	MarkerPlayerNo string `json:"marker_player_no" dynamodbav:"marker_player_no"`
	MarkerSign     string `json:"marker_sign" dynamodbav:"marker_sign"`
}

type PlayerInfo struct {
	GlidNo       string `json:"glid_no"`
	HdcpIndex    string `json:"hdcp_index"`
	PlayerName   string `json:"player_name"`
	Birthday     string `json:"birthday"`
	Gender       int    `json:"gender"`
	HomeClubName string `json:"home_club_name"`
}

type JoinedPlayer struct {
	PlayerNo      int     `json:"player_no" binding:"required,min=1"`
	PlayerName    string  `json:"player_name"`
	Birthday      *string `json:"birthday"` // eg 1942/10/13
	Gender        *int    `json:"gender"`
	GlidNo        string  `json:"glid_no"`
	TeeId         *string `json:"tee_id"`
	Hdcp          *string `json:"hdcp"`
	HdcpIndex     *string `json:"hdcp_index"`
	PlayingHdcp   *string `json:"playing_hdcp"`
	OfficeKey     string  `json:"office_key"`
	PlayDate      string  `json:"play_date"`
	CartNo        int     `json:"cart_no" binding:"required,min=1"`
	CourseIndex   *int    `json:"course_index"`
	IsPaid        bool    `json:"is_paid"`
	TeamClassType *int    `json:"team_class_type"`
}
